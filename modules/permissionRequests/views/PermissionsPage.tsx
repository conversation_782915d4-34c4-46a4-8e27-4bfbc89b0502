import { observer } from 'mobx-react-lite';
import React, { useEffect } from 'react';
import { PermissionsPageStore } from './store/PermissionsPageStore';
import containerDi from '../../_common/view/di/container';
import { typesDi } from '../../_common/view/di/types-di';
import { ContainerTabPage } from '../../_common/view/layout';
import { DataGrid } from '../../_common/view/layout/DataGrid';
import { windowConst } from '../../_common/view/const';
import { DivError } from '../../_common/view/components';
import { PermissionRequestsFilter } from './components/PermissionRequestsFilter';
import { PermissionRequestsMenu } from './components/PermissionRequestsMenu';

const store: PermissionsPageStore = containerDi.get<PermissionsPageStore>(
  typesDi.PermissionsPageStore,
);

export const PermissionsPage = observer(() => {
  useEffect((): void => {
    store.init();
  }, []);

  if (!store.isInitiate) return <></>;
  return (
    <ContainerTabPage>
      <PermissionRequestsMenu />
      <div className="vspan0 container-form">
        <div className="container-wrap">
          <div className="container">
            <PermissionRequestsFilter store={store} />
            <div className="vspan0">
              <DataGrid
                setRows={store.setRows}
                rows={store.rows}
                header={store.header}
                footer={store.footer}
                isLoading={store.isLoading}
                onDoubleClick={windowConst.access_request}
                setTableHeader={store.setHeader}
                onRowSelect={store.setSelectedRow}
                total={store.total}
                onPageChange={store.setPage}
                resizeable={true}
                onResize={store.setHeader}
                getTable={store.getTable}
                checkboxes={true}
                storageKeyPrefix={store.storageKeyPrefix}
              />
              <DivError setErrors={store.setErrors} errors={Object.values(store.errors)} />
            </div>
          </div>
        </div>
      </div>
    </ContainerTabPage>
  );
});
