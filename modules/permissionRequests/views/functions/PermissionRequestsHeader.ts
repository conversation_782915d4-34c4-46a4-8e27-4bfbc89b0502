import { ITableHeader } from '../../../_common/view/interfaces';

export const PermissionRequestsHeader = (): Array<ITableHeader> => {
  return [
    {
      value: 'ID',
      width: 45,
      name: 'id',
    },
    {
      value: 'Статус',
      width: 150,
      name: 'status',
    },
    {
      value: 'Время запроса',
      width: 150,
      name: 'created_at',
    },
    {
      value: 'Запросил',
      width: 120,
      name: 'author',
    },
    {
      value: 'Отдел',
      width: 100,
      name: 'department',
    },
    {
      value: 'Запрашиваемый доступ',
      width: 200,
      name: 'access',
    },
    {
      value: 'Решение',
      width: 150,
      name: 'decision',
    },
    {
      value: 'Предоставил/Отклонил',
      width: 150,
      name: 'decision_user',
    },
    {
      value: 'Время предоставления/отклонения',
      width: 200,
      name: 'decision_time',
    },
    { value: '', width: 'auto', name: '' },
  ];
};
