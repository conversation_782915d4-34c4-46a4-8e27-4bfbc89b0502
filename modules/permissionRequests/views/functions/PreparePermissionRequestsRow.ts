import { IPermissionRequestsRow } from '../interfaces/IPermissionRequestsRow';
import { ITableRow } from '../../../_common/view/interfaces';
import { GetPermissionName } from './GetPermissionName';

export const PreparePermissionRequestsRow = (rows: Array<IPermissionRequestsRow>): Array<ITableRow> => {
  return Object.values(rows).flatMap((item: IPermissionRequestsRow): ITableRow => {
    return {
      checkboxSelect: false,
      id: item.id,
      group: false,
      columns: [
        { name: 'id', value: item.id },
        { name: 'status', value: item.status },
        { name: 'created_at', value: item.created_at },
        { name: 'author', value: item.author },
        { name: 'department', value: item.department },
        { name: 'access_name', value: GetPermissionName(item.access_group, item.access) },
        { name: 'decision', value: item.decision },
        { name: 'decision_user', value: item.decision_user },
        { name: 'decision_time', value: item.decision_time },
        { value: '', name: '' },
      ],
    };
  });
};
