import { DebitPermissions } from '../../../_common/view/permissions/DebitPermissions';
import { FinRequestPermissions } from '../../../_common/view/permissions/FinRequestPermissions';
import { MessagesPermissions } from '../../../_common/view/permissions/MessagesPermissions';
import { PermissionGroupType } from '../../../_common/view/types';
import { DealsRentPermissions } from '../../../_common/view/permissions/DealsRentPermissions';

const PERMISSION_MAP = new Map([
  [
    PermissionGroupType.debit,
    {
      name: 'Дебиторка',
      getPermissions: () => DebitPermissions,
    },
  ],
  [
    PermissionGroupType.finRequests,
    {
      name: 'Фин. заявки',
      getPermissions: () => FinRequestPermissions,
    },
  ],
  [
    PermissionGroupType.dealRent,
    {
      name: 'Сделка (Аренда)',
      getPermissions: () => DealsRentPermissions,
    },
  ],
  [
    PermissionGroupType.messages,
    {
      name: 'Уведомления',
      getPermissions: () => MessagesPermissions,
    },
  ],
]);

export const GetPermissionName = (group: PermissionGroupType, key: string): string => {
  const config = PERMISSION_MAP.get(group);
  if (!config) return '';

  const permissionName =
    config
      .getPermissions()
      .flatMap((g) => g.permissions)
      .find((p) => p.key === key)?.name || '';

  return `${config.name} - ${permissionName}`;
};
