import React, { useEffect } from 'react';
import { observer } from 'mobx-react-lite';
import { ContainerForm, ContainerPage } from '../../_common/view/layout';
import { CloseButton, DivError, FormColumns2, FormGroup, Input } from '../../_common/view/components';
import containerDi from '../../_common/view/di/container';
import { typesDi } from '../../_common/view/di/types-di';
import { PermissionRequestStatuses } from './constants/PermissionRequestStatuses';
import { PermissionCardStore } from './store/PermissionCardStore';
import { PermissionCardMenu } from './components/PermissionCardMenu';
import { Button } from '../../_common/view/layout/buttons/Button';
import { IconConst } from '../../_common/view/const';
import { GetPermissionName } from './functions/GetPermissionName';

const store: PermissionCardStore = containerDi.get<PermissionCardStore>(typesDi.PermissionCardStore);

export const PermissionCard = observer(() => {
  useEffect(() => {
    store.init();
  }, []);

  if (!store.isInitiate) {
    return <></>;
  }
  return (
    <div>
      <ContainerPage>
        <ContainerForm>
          <PermissionCardMenu />
          <div className="vspan0 scroll-y scroll-padding">
            <div className="form-horizontal label-medium">
              <FormColumns2>
                <li>
                  <FormGroup label="Время запроса">
                    <Input name="" onChange={null} readonly={true} value={store.model.created_at} />
                  </FormGroup>
                </li>
                <li>
                  <FormGroup label="Запросил">
                    <Input name="" onChange={null} readonly={true} value={store.model.author} />
                  </FormGroup>
                </li>
                <li>
                  <FormGroup label="Отдел">
                    <Input name="" onChange={null} readonly={true} value={store.model.department} />
                  </FormGroup>
                </li>
                <li>
                  <FormGroup label="Запрашиваемый доступ">
                    <Input
                      name=""
                      onChange={null}
                      readonly={true}
                      value={GetPermissionName(store.model.access_group, store.model.access)}
                    />
                  </FormGroup>
                </li>
                <li>
                  <FormGroup label="Статус">
                    <Input name="" onChange={null} readonly={true} value={store.model.status} />
                  </FormGroup>
                </li>
                <li>
                  <FormGroup label="Решение">
                    <Input name="" onChange={null} readonly={true} value={store.model.decision} />
                  </FormGroup>
                </li>
                <li>
                  <FormGroup label="Предоставил/Отклонил">
                    <Input name="" onChange={null} readonly={true} value={store.model.decision_user} />
                  </FormGroup>
                </li>
                <li>
                  <FormGroup label="Время предоставления/отклонения">
                    <Input name="" onChange={null} readonly={true} value={store.model.decision_time} />
                  </FormGroup>
                </li>
              </FormColumns2>
            </div>
          </div>
          <div className="buttons">
            <CloseButton
              readonly={true}
              setReadonly={() => {}}
              setDefaultModel={() => {}}
              defaultModel={{}}
              isLoading={store.isLoading}
            />
            <Button
              icon={IconConst.ok}
              onClick={(e) => {
                e.preventDefault();
                store.changeDecision(PermissionRequestStatuses.ALLOWED).then();
              }}
              disabled={store.isLoading}
            >
              Предоставить доступ
            </Button>
            <Button
              onClick={(e) => {
                e.preventDefault();
                store.changeDecision(PermissionRequestStatuses.DENIED).then();
              }}
              disabled={store.isLoading}
            >
              <>
                <i className="icon-ban-circle" />
                Отклонить
              </>
            </Button>
          </div>
        </ContainerForm>
      </ContainerPage>
      <DivError errors={Object.values(store.errors)} setErrors={store.setErrors} />
    </div>
  );
});
