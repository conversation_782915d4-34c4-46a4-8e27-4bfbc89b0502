import { inject, injectable } from 'inversify';
import { IPermissionRequestsRow } from '../interfaces/IPermissionRequestsRow';
import { typesDi } from '../../../_common/view/di/types-di';
import { HttpService } from '../../../_common/view/services';
import { IFooter, IPermissionRequest } from '../../../_common/view/interfaces';
import { IStatusRequest } from '../interfaces/IStatusRequest';
import { IPermissionRequestsModel } from '../interfaces/IPermissionRequestsModel';
import { IPermissionList } from '../interfaces/IPermissionList';

@injectable()
export class PermissionRequestsRepository {
  constructor(@inject(typesDi.HttpService) private httpService: HttpService) {}

  async getTable(data: {}): Promise<Array<IPermissionRequestsRow>> {
    return await this.httpService.post('/permission-requests/index-api/', data);
  }

  async getPermissionsList(data: { access_group: string; user_id: number }): Promise<IPermissionList> {
    return await this.httpService.post('/settings/permission/index-api/', data);
  }

  async save(data: IPermissionRequestsModel): Promise<IPermissionRequestsRow> {
    return await this.httpService.post('/permission-requests/index-api/save/', data);
  }

  async sendRequest(data: IPermissionRequest): Promise<void> {
    await this.httpService.post('/permission-requests/index-api/save-request/', data);
  }

  async saveStatus(data: IStatusRequest): Promise<void> {
    await this.httpService.post('/permission-requests/index-api/save-status/', data);
  }

  async getPagination(data: {}): Promise<IFooter> {
    return await this.httpService.post('/permission-requests/index-api/pagination/', data);
  }

  async getMultiOptions(data: { access_group: string }): Promise<any> {
    return await this.httpService.post('/settings/permission/index-api/multi-options/', data);
  }
}
