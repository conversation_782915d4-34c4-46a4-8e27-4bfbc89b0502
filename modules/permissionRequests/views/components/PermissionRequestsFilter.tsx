import React, { FC } from 'react';
import { observer } from 'mobx-react-lite';
import { PermissionsPageStore } from '../store/PermissionsPageStore';
import { Alert, DropButton, MultiCheckbox } from '../../../_common/view/components';
import { UpdateButton } from '../../../_common/view/layout/buttons';
import { useTranslation } from 'react-i18next';
import { PermissionRequestStatuses } from '../constants/PermissionRequestStatuses';
import { Button } from '../../../_common/view/layout/buttons/Button';
import { IconConst } from '../../../_common/view/const';
import { ResetWidthButton } from '../../../_common/view/components/CustomizeTable/ResetWidthButton';

interface AccessRequestsFilterProps {
  store: PermissionsPageStore;
}

export const PermissionRequestsFilter: FC<AccessRequestsFilterProps> = observer(
  ({ store }: AccessRequestsFilterProps) => {
    const { t } = useTranslation('common');
    return (
      <>
        <ul className="toolbar-filter">
          <li>
            <UpdateButton onClick={() => store.getTable()} title={t('Обновить')} disabled={store.isLoading} />
          </li>
          <li>
            <Button
              icon={IconConst.ok}
              onClick={() => {
                store.saveDecision(PermissionRequestStatuses.ALLOWED).then();
              }}
              disabled={store.isLoading}
            >
              Предоставить доступ
            </Button>
          </li>
          <li>
            <Button
              onClick={() => {
                store.saveDecision(PermissionRequestStatuses.DENIED).then();
              }}
              disabled={store.isLoading}
            >
              <>
                <i className="icon-ban-circle" />
                Отклонить
              </>
            </Button>
          </li>
          <li className="divider" />
          <li>
            <MultiCheckbox
              values={store.user_filter}
              readonly={store.isLoading}
              name="user_filter"
              options={store.userOptions}
              caption="Пользователи"
              DropButton={DropButton}
              onChange={store.setMultiCheck}
            />
          </li>
          <li>
            <MultiCheckbox
              values={store.department_filter}
              readonly={store.isLoading}
              name="department_filter"
              options={store.departmentOptions}
              caption="Отделы"
              DropButton={DropButton}
              onChange={store.setMultiCheck}
            />
          </li>
          <li>
            <MultiCheckbox
              values={store.status_filter}
              readonly={store.isLoading}
              name="status_filter"
              options={store.statusOptions}
              caption="Статусы"
              DropButton={DropButton}
              onChange={store.setMultiCheck}
            />
          </li>
          <li className="pull-right">
            <ResetWidthButton storageKeyPrefix={store.storageKeyPrefix} />
          </li>
        </ul>
        {store.alertMessage && (
          <Alert
            popupMessageClass="no-max-height"
            message={store.alertMessage}
            title={store.alertTitle}
            onClickOk={() => {
              store.alertMessage = '';
              store.alertTitle = null;
            }}
          />
        )}
      </>
    );
  },
);
