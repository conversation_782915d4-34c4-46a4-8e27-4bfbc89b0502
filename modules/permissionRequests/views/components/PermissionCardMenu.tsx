import { MenuHorizontal } from '../../../_common/view/menu';
import { IMenuItem } from '../../../_common/view/interfaces';
import { IconConst } from '../../../_common/view/const';
import { getIdFromUrl } from '../../../_common/view/handlers/functions';
import { ChangeIdCard } from '../../../_common/view/layout';

export const PermissionCardMenu = () => {
  const menuItems: Array<IMenuItem> = [
    {
      href: '/permission-requests/card/',
      params: { id: getIdFromUrl() },
      icon: IconConst.lock,
      title: 'Запрос доступа',
    },
  ];

  return (
    <MenuHorizontal items={menuItems}>
      <ChangeIdCard />
    </MenuHorizontal>
  );
};
