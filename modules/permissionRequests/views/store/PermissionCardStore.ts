import { injectable } from 'inversify';
import { makeAutoObservable } from 'mobx';
import containerDi from '../../../_common/view/di/container';
import { typesDi } from '../../../_common/view/di/types-di';
import { getTitle } from '../../../_common/view/handlers/functions';
import { PermissionRequestsRepository } from '../repositories/PermissionRequestsRepository';
import { IPermissionRequestsRow } from '../interfaces/IPermissionRequestsRow';
import { ObjectService } from '../../../_common/view/services';
import { errorHandler } from '../../../_common/view/handlers/errorHandler';

@injectable()
export class PermissionCardStore {
  private repository: PermissionRequestsRepository;
  public isInitiate: boolean = false;
  public isLoading: boolean = false;
  public model: IPermissionRequestsRow = Object({});
  public errors = {};

  public constructor() {
    this.repository = containerDi.get<PermissionRequestsRepository>(typesDi.PermissionRequestsRepository);
    makeAutoObservable(this, {}, { autoBind: true });
  }

  /**
   * Инициализация хранилища
   * @return void
   * */
  public init(): void {
    document.title = getTitle(document.title);
    this.setModel((document as any).data);
    this.isInitiate = true;
  }

  set loading(value: boolean) {
    this.isLoading = value;
  }

  /**
   * Присвоение ошибок карточки
   * @param {any} value
   * @return void
   * */
  public setErrors(value: any): void {
    this.errors = value;
  }

  /**
   * Сохранить модель
   * @param {IMistakeModel} model
   * @return void
   * */
  public setModel(model: IPermissionRequestsRow): void {
    this.model = ObjectService.copy(model);
  }

  private updateOwnerTable(): void {
    try {
      window.opener?.getTable && window.opener?.getTable();
    } catch (e) {}
  }

  /**
   * Изменить статус запроса
   * @param {number} status
   * @return Promise<void>
   * */
  public async changeDecision(status: number): Promise<void> {
    try {
      this.loading = true;
      const data = await this.repository.save({
        id: this.model.id,
        status: status,
        access: this.model.access,
        access_group: this.model.access_group,
      });
      this.setModel(data);
      this.updateOwnerTable();
    } catch (e) {
      errorHandler(e, this.setErrors);
    } finally {
      this.loading = false;
    }
  }
}
