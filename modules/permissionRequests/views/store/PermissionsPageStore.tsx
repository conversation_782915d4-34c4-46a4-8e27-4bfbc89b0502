import { injectable } from 'inversify';
import { makeAutoObservable } from 'mobx';
import { PermissionRequestsRepository } from '../repositories/PermissionRequestsRepository';
import { IPermissionRequestsQueryParams } from '../interfaces/IPermissionRequestsQueryParams';
import { IPermissionRequestsRow } from '../interfaces/IPermissionRequestsRow';
import { PreparePermissionRequestsRow } from '../functions/PreparePermissionRequestsRow';
import { PermissionRequestsHeader } from '../functions/PermissionRequestsHeader';
import { IFooter, IOption, ITableFooter, ITableHeader, ITableRow } from '../../../_common/view/interfaces';
import containerDi from '../../../_common/view/di/container';
import { typesDi } from '../../../_common/view/di/types-di';
import { getSelectedRows, getTitle } from '../../../_common/view/handlers/functions';
import { errorHandler } from '../../../_common/view/handlers/errorHandler';
import { multiCheckboxChangeArray } from '../../../_common/view/handlers/changeHandlers';

@injectable()
export class PermissionsPageStore {
  private repository: PermissionRequestsRepository;
  public isInitiate: boolean = false;
  public pageLimit: number = 100;
  public footer: ITableFooter = {
    count: 0,
    page: 0,
    limit: this.pageLimit,
  };
  public total: Array<ITableRow> = [];
  public isLoading: boolean = false;
  public rows: Array<ITableRow> = [];
  public errors: {} = {};
  public header: Array<ITableHeader> = [];
  public selectedRow: number = 0;
  public statusOptions: Array<IOption> = [];
  public departmentOptions: Array<IOption> = [];
  public userOptions: Array<IOption> = [];
  public status_filter: Array<number> = [];
  public department_filter: Array<number> = [];
  public user_filter: Array<number> = [];
  public alertMessage: string = '';
  public alertTitle: string = '';
  public storageKeyPrefix = 'permission_requests.';

  public constructor() {
    makeAutoObservable(this, {}, { autoBind: true });
    this.repository = containerDi.get<PermissionRequestsRepository>(typesDi.PermissionRequestsRepository);
  }

  public init(): void {
    document.title = getTitle(document.title);
    this.header = PermissionRequestsHeader();
    this.userOptions = (document as any).user_options ?? [];
    this.statusOptions = (document as any).status_options ?? [];
    this.departmentOptions = (document as any).department_options ?? [];
    this.status_filter = [0];
    this.department_filter = this.departmentOptions.map((option: IOption): number => Number(option.key));
    this.user_filter = this.userOptions.map((option: IOption): number => Number(option.key));
    (window as any).getTable = () => this.getTable();
    this.getTable().then();
    this.isInitiate = true;
  }

  set loading(value: boolean) {
    this.isLoading = value;
  }

  set newFooter(value: ITableFooter) {
    this.footer = value;
  }

  /**
   * Получить таблицу
   * @return Promise<void>
   */
  public async getTable(): Promise<void> {
    try {
      this.loading = true;
      const queryParams: IPermissionRequestsQueryParams = this.getQueryParams();
      this.getFooter().then();
      const response: Array<IPermissionRequestsRow> = await this.repository.getTable(queryParams);
      const preparedRows: Array<ITableRow> = PreparePermissionRequestsRow(response);
      this.setRows(preparedRows);
    } catch (e) {
      errorHandler(e, this.setErrors);
    } finally {
      this.loading = false;
    }
  }

  /**
   * Получить футер таблицы
   * @return Promise<void>
   */
  public async getFooter(): Promise<void> {
    try {
      this.errors = {};
      const response: IFooter = await this.repository.getPagination(this.getQueryParams());
      this.newFooter = {
        count: response.count,
        limit: this.footer.limit,
        page: response.count <= this.footer.limit ? 0 : this.footer.page,
        pages: response.pagination,
      };
    } catch (e) {
      errorHandler(e, this.setErrors);
    }
  }

  /**
   * Получить параметры запроса
   * @return IPermissionRequestsQueryParams
   * @private
   */
  private getQueryParams(): IPermissionRequestsQueryParams {
    return {
      page: this.footer.page,
      limit: this.footer.limit,
      status_filter: this.status_filter,
      department_filter: this.department_filter,
      user_filter: this.user_filter,
    };
  }

  /**
   * Задать страницу
   * @param {number} page
   * @return Promise<void>
   * @param page
   */
  public setPage(page: number): void {
    this.footer.page = page;
    this.getTable().then();
  }

  /**
   * Присвоение ошибок
   * @param {any} value
   * @return void
   * @param value
   */
  public setErrors(value: any): void {
    this.errors = value;
  }

  /**
   * Выбор строки таблицы
   * @param {number} value
   * @return void
   */
  public setSelectedRow(value: number): void {
    this.selectedRow = value;
  }

  /**
   * Изменение параметров хранилища
   * @param {string} name
   * @param {number | string | (number|string)[]} value
   * @return void
   */
  public setAttribute({ name, value }: { name: string; value: number | string | (number | string)[] }): void {
    this[name] = value;
  }

  public setMultiCheck({ name, value, checked }): void {
    this[name] = multiCheckboxChangeArray({ value, checked, array: this[name] });
  }

  /**
   * Изменение строк таблицы
   * @param {Array<ITableRow>} value
   * @return void
   */
  public setRows(value: Array<ITableRow>): void {
    this.rows = value;
  }

  /**
   * Изменение заголовка таблицы
   * @param {Array<ITableHeader>} value
   * @return void
   */
  public setHeader(value: Array<ITableHeader>): void {
    this.header = value;
  }

  /**
   * Сохранить статус выбранных запросов
   * @param {number} status
   * @return Promise<void>
   */
  public async saveDecision(status: number): Promise<void> {
    const rowsIds: Array<string | number> = getSelectedRows(this.rows);
    if (rowsIds.length === 0) {
      this.alertTitle = 'Задать статус запросов';
      this.alertMessage = 'Выберите запросы';
      return;
    }
    try {
      this.loading = true;
      await this.repository.saveStatus({ id_list: rowsIds, status: status });
      await this.getTable();
    } catch (e) {
      errorHandler(e, this.setErrors);
    } finally {
      this.loading = false;
    }
  }
}
