<?php

namespace modules\permissionRequests\validators;

use yii\base\Model;

class PermissionRequestListValidator extends Model
{
    public int|null $page;
    public int|null $limit;
    /** @var array<int> $status_filter */
    public array|null $status_filter;
    /** @var array<int> $department_filter */
    public array|null $department_filter;
    /** @var array<int> $user_filter */
    public array|null $user_filter;

    /**
     * @return array<mixed>
     **/
    public function rules(): array
    {
        return [[['page'], 'default', 'value' => 0], [['limit'], 'default', 'value' => -1]];
    }
}
