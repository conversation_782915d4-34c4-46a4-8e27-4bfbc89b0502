<?php

namespace modules\permissionRequests\validators;

use yii\base\Model;

class SavePermissionRequestValidator extends Model
{
    public int|null $id = null;
    public string|null $access_group = null;
    public string|null $access = null;
    public int|null $status = null;
    public array|string|null $value = null;

    /**
     * @return array<mixed>
     **/
    public function rules(): array
    {
        return [[['status'], 'default', 'value' => 0], [['value'], 'safe'], [['value'], 'validateValue']];
    }

    /**
     * Validate value field
     * @param string $attribute
     * @param mixed $params
     */
    public function validateValue($attribute, $params): void
    {
        if ($this->value !== null) {
            if (is_array($this->value)) {
                foreach ($this->value as $item) {
                    if (!is_string($item)) {
                        $this->addError($attribute, 'Array values must be strings');
                        return;
                    }
                }
            } elseif (is_string($this->value)) {
                $decoded = json_decode($this->value, true);
                if (json_last_error() !== JSON_ERROR_NONE) {
                    $this->addError($attribute, 'Invalid JSON format');
                    return;
                }
                if (!is_array($decoded)) {
                    $this->addError($attribute, 'Value must be an array when provided');
                    return;
                }
            } else {
                $this->addError($attribute, 'Value must be an array or JSON string');
            }
        }
    }
}
