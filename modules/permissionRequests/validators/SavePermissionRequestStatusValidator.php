<?php

namespace modules\permissionRequests\validators;

use yii\base\Model;

class SavePermissionRequestStatusValidator extends Model
{
    /** @var array<int> $id_list */
    public array|null $id_list = null;
    public int|null $status = null;

    /**
     * @return array<mixed>
     **/
    public function rules(): array
    {
        return [['id_list', 'default', 'value' => []], [['status'], 'required']];
    }
}
