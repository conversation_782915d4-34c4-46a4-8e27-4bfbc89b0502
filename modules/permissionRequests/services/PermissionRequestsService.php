<?php

namespace modules\permissionRequests\services;

use common\models\ActiveQueryItr;
use common\models\PermissionRequestsModel;
use common\models\UsersModel;
use common\services\AccessService;
use common\services\common\DbOffsetService;
use common\services\common\NumberService;
use common\services\common\PaginationService;
use common\services\common\StringService;
use common\types\PermissionRequestFinishedStatusTypes;
use common\types\PermissionRequestStatusTypes;
use modules\permissionRequests\entities\AccessRequestEntity;
use modules\permissionRequests\validators\PermissionRequestListValidator;
use modules\permissionRequests\validators\SavePermissionRequestStatusValidator;
use modules\permissionRequests\validators\SavePermissionRequestValidator;
use Yii;
use yii\web\HttpException;
use yii\web\NotFoundHttpException;

class PermissionRequestsService
{
    private PermissionRequestsModel $model;

    public function __construct(PermissionRequestsModel $model = null)
    {
        if (\is_null($model)) {
            $this->model = new PermissionRequestsModel();
        } else {
            $this->model = $model;
        }
    }

    /**
     * Возвращает список доступов
     * @param PermissionRequestListValidator $validator
     * @return array<AccessRequestEntity>
     * @throws \Exception
     */
    public function getList(PermissionRequestListValidator $validator): array
    {
        $count = NumberService::toInt($this->getListQuery($validator)->count());
        $offset = NumberService::toInt(DbOffsetService::getOffset($validator->page * $validator->limit, $count));
        $query = $this->getListQuery($validator);
        $accessRequests = $query->limitOffset($validator->limit, $offset)->all();

        $result = [];
        foreach ($accessRequests as $accessRequest) {
            /** @var PermissionRequestsModel $accessRequest */
            $result[] = $this->getPermissionRequestAttributes($accessRequest);
        }
        return $result;
    }

    /**
     * Получить данные запроса доступа
     * @param PermissionRequestsModel $accessRequest
     * @return AccessRequestEntity
     * @throws \Exception
     */
    private function getPermissionRequestAttributes(PermissionRequestsModel $accessRequest): AccessRequestEntity
    {
        $accessRequestEntity = new AccessRequestEntity();
        $accessRequestEntity->id = $accessRequest->id;
        $accessRequestEntity->status =
            $accessRequest->status == PermissionRequestStatusTypes::NEW
                ? PermissionRequestFinishedStatusTypes::get(PermissionRequestFinishedStatusTypes::NEW)
                : PermissionRequestFinishedStatusTypes::get(PermissionRequestFinishedStatusTypes::FINISHED);
        $accessRequestEntity->created_at = StringService::getTimeFromInt(
            NumberService::toInt(strtotime($accessRequest->created_at)),
        );

        $accessRequestEntity->author = $accessRequest->getAuthorName();
        $accessRequestEntity->department = $accessRequest->getDepartment();
        $accessRequestEntity->access = $accessRequest->access;
        $accessRequestEntity->access_group = $accessRequest->access_group;
        $accessRequestEntity->decision = $accessRequest->getDecision();
        $accessRequestEntity->decision_user = $accessRequest->getDecisionUser();
        $accessRequestEntity->decision_time = StringService::getTimeFromInt(
            NumberService::toInt(strtotime($accessRequest->decision_time)),
        );
        return $accessRequestEntity;
    }

    /**
     * Пагинация
     * @param PermissionRequestListValidator $validator
     * @return array<mixed>
     * @throws \Exception
     */
    public function getPagination(PermissionRequestListValidator $validator): array
    {
        $query = $this->getListQuery($validator);
        $count = NumberService::toInt($query->count());
        $offset = NumberService::toInt(DbOffsetService::getOffset($validator->page * $validator->limit, $count));
        return [
            'count' => $count,
            'pagination' => PaginationService::create($count, NumberService::toInt($validator->limit), $offset),
        ];
    }

    /**
     * Получить запрос для списка доступов
     * @param PermissionRequestListValidator $validator
     * @return ActiveQueryItr<PermissionRequestsModel>
     * @throws \Exception
     */
    private function getListQuery(PermissionRequestListValidator $validator): ActiveQueryItr
    {
        $statuses = $validator->status_filter ?? [];
        if (in_array(PermissionRequestFinishedStatusTypes::FINISHED, $statuses)) {
            $statuses[] = PermissionRequestStatusTypes::DENIED;
        }
        $query = $this->model::find()->orderBy('id DESC');
        $query->andWhere(['department' => $validator->department_filter]);
        $query->andWhere(['status' => $statuses]);
        $query->andWhere(['OR', ['decision_user' => $validator->user_filter], ['author' => $validator->user_filter]]);
        return $query;
    }

    /**
     * Получить запрос доступа
     * @param int $id
     * @return AccessRequestEntity|null
     * @throws \Exception
     */
    public function get(int $id): AccessRequestEntity|null
    {
        if (!$id) {
            $accessRequest = new $this->model();
        } else {
            $accessRequest = $this->model::findOne([
                'id' => $id,
            ]);
        }
        if (!$accessRequest) {
            throw new NotFoundHttpException();
        }

        return $this->getPermissionRequestAttributes($accessRequest);
    }

    /**
     * Сохранить запрос доступа
     * @param SavePermissionRequestValidator $validator
     * @return int
     * @throws HttpException
     */
    public function save(SavePermissionRequestValidator $validator): int
    {
        if (!$validator->id) {
            $user = UsersModel::findOneSelect(['id' => Yii::$app->user->getId()], ['department', 'id']);
            /**@var PermissionRequestsModel $accessRequest * */
            $accessRequest = new $this->model();
            $accessRequest->created_at = date('Y-m-d H:i:s');
            $accessRequest->author = $user->id ?? 0;
            $accessRequest->department = $user->department ?? 0;
        } else {
            $accessRequest = $this->model::findOne([
                'id' => $validator->id,
            ]);
        }

        if (!$accessRequest) {
            throw new NotFoundHttpException();
        }

        $accessRequest->setAttributes($validator->attributes, false);

        if ($validator->value !== null) {
            if (is_array($validator->value)) {
                $accessRequest->setValueFromArray($validator->value);
            } else {
                $decoded = json_decode($validator->value, true);
                if (is_array($decoded)) {
                    $accessRequest->setValueFromArray($decoded);
                } else {
                    $accessRequest->value = $validator->value;
                }
            }
        }

        if ($validator->status !== PermissionRequestStatusTypes::NEW) {
            $accessRequest->decision_user = NumberService::toInt(Yii::$app->getUser()->getId());
            $accessRequest->decision_time = date('Y-m-d H:i:s');
        }
        $accessRequest->save();
        return $accessRequest->id;
    }

    /**
     * Сохранить статус запросов
     * @param SavePermissionRequestStatusValidator $validator
     * @return void
     * @throws HttpException
     */
    public function saveStatus(SavePermissionRequestStatusValidator $validator): void
    {
        if (!$validator->id_list) {
            return;
        }
        foreach ($validator->id_list as $id) {
            $accessRequest = $this->model::findOne(['id' => $id]);
            if (!$accessRequest) {
                continue;
            }
            $accessRequest->status = NumberService::toInt($validator->status);
            $accessRequest->decision_user = NumberService::toInt(Yii::$app->getUser()->getId());
            $accessRequest->decision_time = date('Y-m-d H:i:s');
            $accessRequest->save();
        }
    }

    /**
     * Количество новых запросов
     * @return int
     */
    public static function getCountNewRequests(): int
    {
        return NumberService::toInt(
            PermissionRequestsModel::find()
                ->where(['status' => PermissionRequestStatusTypes::NEW])
                ->count(),
        );
    }

    /**
     * Доступ к запросам доступов
     * @return void
     * @throws HttpException
     * @throws \Exception
     */
    public static function access(): void
    {
        if (!AccessService::check('personal', 'enableAccess')) {
            throw new HttpException(403, 'Доступ запрещен');
        }
    }
}
