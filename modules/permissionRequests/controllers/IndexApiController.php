<?php

namespace modules\permissionRequests\controllers;

use backend\controllers\ApiController;
use backend\validators\common\ValidatorHelpers;
use modules\permissionRequests\entities\AccessRequestEntity;
use modules\permissionRequests\services\PermissionRequestsService;
use modules\permissionRequests\validators\PermissionRequestListValidator;
use modules\permissionRequests\validators\SavePermissionRequestStatusValidator;
use modules\permissionRequests\validators\SavePermissionRequestValidator;
use yii\web\HttpException;

final class IndexApiController extends ApiController
{
    /**
     * Получение списка запросов доступов
     * @return array<mixed>
     * @throws \Exception
     */
    public function actionIndex(): array
    {
        PermissionRequestsService::access();
        $validator = ValidatorHelpers::createFromPost(PermissionRequestListValidator::class);
        if (!$validator->validate()) {
            return $this->getReactItrErrors($validator);
        }
        return (new PermissionRequestsService())->getList($validator);
    }

    /**
     * Пагинация
     * @return array<mixed>
     * @throws \Exception
     */
    public function actionPagination(): array
    {
        PermissionRequestsService::access();
        $validator = ValidatorHelpers::createFromPost(PermissionRequestListValidator::class);
        if (!$validator->validate()) {
            return $this->getReactItrErrors($validator);
        }
        return (new PermissionRequestsService())->getPagination($validator);
    }

    /**
     * Сохранить решение
     * @return AccessRequestEntity|array<mixed>
     * @throws HttpException
     * @throws \Exception
     */
    public function actionSave(): AccessRequestEntity|array
    {
        PermissionRequestsService::access();
        $validator = ValidatorHelpers::createFromPost(SavePermissionRequestValidator::class);
        if (!$validator->validate()) {
            return $this->getReactItrErrors($validator);
        }
        $service = new PermissionRequestsService();
        $id = $service->save($validator);
        return $service->get($id) ?? [];
    }

    /**
     * Сохранить запрос на предоставление доступа
     * @return AccessRequestEntity|array<mixed>
     * @throws HttpException
     * @throws \Exception
     * @api
     */
    public function actionSaveRequest(): AccessRequestEntity|array
    {
        $validator = ValidatorHelpers::createFromPost(SavePermissionRequestValidator::class);
        if (!$validator->validate()) {
            return $this->getReactItrErrors($validator);
        }
        $service = new PermissionRequestsService();
        $id = $service->save($validator);
        return $service->get($id) ?? [];
    }

    /**
     * @api
     * Сохранить статус запросов
     * @return array<mixed>
     * @throws HttpException
     */
    public function actionSaveStatus(): array
    {
        PermissionRequestsService::access();
        $validator = ValidatorHelpers::createFromPost(SavePermissionRequestStatusValidator::class);
        if (!$validator->validate()) {
            return $this->getReactItrErrors($validator);
        }
        (new PermissionRequestsService())->saveStatus($validator);
        return [];
    }
}
