<?php

namespace modules\permissionRequests\controllers;

use common\services\common\NumberService;
use common\types\Error404Types;
use modules\_common\ItrController;
use modules\permissionRequests\services\PermissionRequestsService;
use Yii;
use yii\web\HttpException;

final class CardController extends ItrController
{
    /**
     * Получение карточки запроса доступа
     * @return string
     * @throws HttpException
     * @throws \Exception
     */
    public function actionIndex(): string
    {
        PermissionRequestsService::access();
        $this->error404Handler = Error404Types::CARD;
        $id = NumberService::toInt(Yii::$app->request->get('id') ?? 0);
        $this->setFirmParams();
        $this->setPageTitle([['title' => 'Запрос доступа', 'id' => NumberService::toInt($id)]]);
        $accessRequest = (new PermissionRequestsService())->get($id);
        return $this->renderReact($accessRequest ?? []);
    }
}
