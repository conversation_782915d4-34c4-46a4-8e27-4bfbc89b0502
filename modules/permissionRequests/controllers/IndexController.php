<?php

namespace modules\permissionRequests\controllers;

use common\models\DepartmentsModel;
use common\models\UsersModel;
use common\types\PermissionRequestFinishedStatusTypes;
use modules\_common\ItrController;
use modules\permissionRequests\services\PermissionRequestsService;
use Yii;

final class IndexController extends ItrController
{
    /**
     * Список запросов доступов
     * @return string
     * @throws \Exception
     */
    public function actionIndex(): string
    {
        PermissionRequestsService::access();
        $this->setMainMenuAccess();
        $this->setRightMenuAccess();
        $this->setFirmParams();
        $this->params['user_options'] = [
            ['key' => 0, 'value' => Yii::t('const', 'Не выбрано')],
            ...UsersModel::getOptions(),
        ];
        $this->params['department_options'] = [
            ['key' => 0, 'value' => 'Не выбрано'],
            ...DepartmentsModel::getOptions(),
        ];
        $this->params['status_options'] = PermissionRequestFinishedStatusTypes::getOptions();
        $this->setPageTitle([['title' => 'Доступы']]);
        return $this->renderReact([]);
    }
}
