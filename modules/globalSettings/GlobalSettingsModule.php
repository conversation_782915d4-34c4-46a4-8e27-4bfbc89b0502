<?php

namespace modules\globalSettings;

use modules\_common\ItrModule;
use modules\globalSettings\currencies\CurrenciesModule;
use modules\globalSettings\customs\CustomsModule;
use modules\globalSettings\exchangeRates\ExchangeRatesModule;
use modules\globalSettings\taxes\TaxesModule;
use yii\console\Application;

class GlobalSettingsModule extends ItrModule
{
    public $layout = 'main';

    public function init()
    {
        parent::init();
        $this->modules = [
            'customs' => [
                'class' => CustomsModule::class,
            ],
            'currencies' => [
                'class' => CurrenciesModule::class,
            ],
            'exchange-rates' => [
                'class' => ExchangeRatesModule::class,
            ],
            'taxes' => [
                'class' => TaxesModule::class,
            ],
        ];
    }

    public function bootstrap($app)
    {
        if ($app instanceof Application) {
            $this->controllerNamespace = 'modules\globalSettings\exchangeRates\controllers\console';
        }
    }
}
