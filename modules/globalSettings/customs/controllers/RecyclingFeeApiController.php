<?php

namespace modules\globalSettings\customs\controllers;

use backend\controllers\ApiController;
use backend\validators\common\ValidatorHelpers;
use modules\globalSettings\index\services\GlobalSettingsService;
use modules\globalSettings\customs\services\RecyclingFeeService;
use modules\globalSettings\customs\validators\RecyclingFeeSaveValidator;
use Yii;
use yii\web\HttpException;

class RecyclingFeeApiController extends ApiController
{
    /**
     * Сохранение записи утилизационного сбора
     * @return array
     * @throws \Exception
     */
    public function actionSave(): array
    {
        GlobalSettingsService::access();
        $validator = ValidatorHelpers::createFromPost(RecyclingFeeSaveValidator::class);
        if (!$validator->validate()) {
            return $this->getReactItrErrors($validator);
        }
        /** @var RecyclingFeeSaveValidator $validator */
        return RecyclingFeeService::save($validator);
    }

    /**
     * Удаление записи утилизационного сбора
     * @return array
     * @throws HttpException
     */
    public function actionDelete(): array
    {
        GlobalSettingsService::access();
        $id = (int) Yii::$app->request->post('id');
        return [
            $id => RecyclingFeeService::delete($id),
        ];
    }

    /**
     * Получение списка утилизационных сборов
     * @return array
     * @throws \Exception
     */
    public function actionList(): array
    {
        return RecyclingFeeService::getList();
    }
}
