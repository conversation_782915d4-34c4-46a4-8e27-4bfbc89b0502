<?php

namespace modules\globalSettings\customs\controllers;

use backend\controllers\ApiController;
use backend\validators\common\ValidatorHelpers;
use modules\globalSettings\index\services\GlobalSettingsService;
use modules\globalSettings\customs\services\ExciseService;
use modules\globalSettings\customs\validators\ExciseSaveValidator;
use Yii;
use yii\web\HttpException;

class ExciseApiController extends ApiController
{
    /**
     * Сохранение записи акциз
     * @throws HttpException
     * @throws \Exception
     */
    public function actionSave(): array
    {
        GlobalSettingsService::access();
        $validator = ValidatorHelpers::createFromPost(ExciseSaveValidator::class);
        if (!$validator->validate()) {
            return $this->getReactItrErrors($validator);
        }
        /** @var ExciseSaveValidator $validator */
        return ExciseService::save($validator);
    }

    /**
     * Удаление записи акциз
     * @throws HttpException
     */
    public function actionDelete(): array
    {
        GlobalSettingsService::access();
        $id = (int) Yii::$app->request->post('id');
        return [
            $id => ExciseService::delete($id),
        ];
    }

    /**
     * Получение списка акциз
     * @throws \Exception
     */
    public function actionList(): array
    {
        return ExciseService::getList();
    }
}
