<?php

namespace modules\globalSettings\customs\controllers;

use modules\globalSettings\customs\controllers\abstractController\AbstractController;
use modules\globalSettings\customs\services\CustomsCollectionService;
use modules\globalSettings\index\services\GlobalSettingsService;
use Yii;
use yii\web\HttpException;

class CustomsCollectionController extends AbstractController
{
    /**
     * Рендер React-приложения
     *
     * @return string
     * @throws HttpException
     */
    public function actionIndex(): string
    {
        GlobalSettingsService::access();
        $this->setFirmParams();
        $id = Yii::$app->request->get('id', 0);
        $this->setPageTitle([['title' => 'Таможенная пошлина', 'id' => $id]]);

        return $this->renderReact(CustomsCollectionService::get($id));
    }
}
