<?php

namespace modules\globalSettings\customs\controllers;

use Exception;
use modules\globalSettings\customs\controllers\abstractController\AbstractController;
use modules\globalSettings\customs\services\CustomsDutyService;
use modules\globalSettings\index\services\GlobalSettingsService;
use Yii;

class CustomsDutyController extends AbstractController
{
    /**
     * Рендер React-приложения
     *
     * @return string
     * @throws Exception
     */
    public function actionIndex(): string
    {
        GlobalSettingsService::access();
        $this->error404Handler = 'error404Card';
        $this->setFirmParams();
        $id = Yii::$app->request->get('id', 0);
        $this->setPageTitle([['title' => 'Таможенная пошлина', 'id' => $id]]);
        return $this->renderReact(CustomsDutyService::get($id));
    }
}
