<?php

namespace modules\globalSettings\customs\controllers;

use modules\globalSettings\customs\controllers\abstractController\AbstractController;
use modules\globalSettings\customs\services\RecyclingFeeService;
use modules\globalSettings\index\services\GlobalSettingsService;
use Yii;
use yii\web\HttpException;

class RecyclingFeeController extends AbstractController
{
    /**
     * Рендер React-приложения
     *
     * @return string
     * @throws HttpException
     */
    public function actionIndex(): string
    {
        GlobalSettingsService::access();
        $this->error404Handler = 'error404Card';
        $this->setFirmParams();
        $id = Yii::$app->request->get('id', 0);
        $this->setPageTitle([['title' => 'Утилизационный сбор', 'id' => $id]]);
        return $this->renderReact(RecyclingFeeService::get($id));
    }

    /**
     * Копирование карточки утилизационного сбора
     * @throws HttpException
     */
    public function actionCopy(): string
    {
        GlobalSettingsService::access();
        $this->error404Handler = 'error404Card';
        $id = Yii::$app->request->get('from');
        $recyclingFee = RecyclingFeeService::get($id);
        if (!$recyclingFee) {
            throw new HttpException(404);
        }
        unset($recyclingFee['id']);
        return $this->renderReact($recyclingFee);
    }
}
