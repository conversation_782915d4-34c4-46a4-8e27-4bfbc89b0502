<?php

namespace modules\globalSettings\customs\controllers;

use backend\controllers\ApiController;
use backend\validators\common\ValidatorHelpers;
use modules\globalSettings\index\services\GlobalSettingsService;
use modules\globalSettings\customs\services\CustomsDutySettingsService;
use modules\globalSettings\customs\validators\CustomsDutySettingsValidator;

class CustomsDutySettingsApiController extends ApiController
{
    /**
     * Сохранение таможенных настроек
     * @return array
     * @throws \Exception
     */
    public function actionSave(): array
    {
        GlobalSettingsService::access();
        $validator = ValidatorHelpers::createFromPost(CustomsDutySettingsValidator::class);
        if (!$validator->validate()) {
            return $this->getReactItrErrors($validator);
        }
        /** @var CustomsDutySettingsValidator $validator */
        return CustomsDutySettingsService::save($validator);
    }
}
