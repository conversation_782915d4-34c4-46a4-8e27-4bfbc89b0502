<?php

namespace modules\globalSettings\customs\controllers;

use Exception;
use modules\_common\ItrController;
use modules\globalSettings\customs\services\CustomsDutySettingsService;
use modules\globalSettings\index\services\GlobalSettingsService;

class CustomsDutySettingsController extends ItrController
{
    /**
     * Рендер React-приложения
     *
     * @return string
     * @throws Exception
     */
    public function actionIndex(): string
    {
        GlobalSettingsService::access();
        $this->setMainMenuAccess();
        $this->setRightMenuAccess();
        $this->setFirmParams();
        $this->setPageTitle([['title' => 'Настройки']]);
        return $this->renderReact(CustomsDutySettingsService::get());
    }
}
