<?php

namespace modules\globalSettings\customs\controllers\abstractController;

use modules\globalSettings\index\services\GlobalSettingsService;
use modules\_common\ItrController;
use yii\web\HttpException;

abstract class AbstractController extends ItrController
{
    /**
     * Рендер списка
     *
     * @return string
     * @throws HttpException
     */
    public function actionList(): string
    {
        GlobalSettingsService::access();
        $this->setMainMenuAccess();
        $this->setRightMenuAccess();

        return $this->renderReact([]);
    }
}
