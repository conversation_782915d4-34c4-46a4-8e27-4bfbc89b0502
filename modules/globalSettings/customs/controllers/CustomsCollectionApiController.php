<?php

namespace modules\globalSettings\customs\controllers;

use backend\controllers\ApiController;
use backend\validators\common\ValidatorHelpers;
use modules\globalSettings\customs\services\CustomsCollectionService;
use modules\globalSettings\customs\validators\CustomsCollectionSaveValidator;
use modules\globalSettings\index\services\GlobalSettingsService;
use Yii;
use yii\web\HttpException;

class CustomsCollectionApiController extends ApiController
{
    /**
     * Сохранение таможенных пошлин
     * @return array
     * @throws HttpException
     */
    public function actionSave(): array
    {
        GlobalSettingsService::access();
        $validator = ValidatorHelpers::createFromPost(CustomsCollectionSaveValidator::class);
        if (!$validator->validate()) {
            return $this->getReactItrErrors($validator);
        }
        /** @var CustomsCollectionSaveValidator $validator */
        return CustomsCollectionService::save($validator);
    }

    /**
     * Удаление таможенных пошлин
     * @return array
     * @throws HttpException
     */
    public function actionDelete(): array
    {
        GlobalSettingsService::access();
        $id = (int) Yii::$app->request->post('id');
        return [
            $id => CustomsCollectionService::delete($id),
        ];
    }

    /**
     * Получение списка таможенных пошлин
     * @return array
     * @throws \Exception
     */
    public function actionList(): array
    {
        return CustomsCollectionService::getList();
    }
}
