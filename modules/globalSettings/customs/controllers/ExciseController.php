<?php

namespace modules\globalSettings\customs\controllers;

use modules\globalSettings\customs\controllers\abstractController\AbstractController;
use modules\globalSettings\customs\services\ExciseService;
use modules\globalSettings\index\services\GlobalSettingsService;
use Yii;
use yii\web\HttpException;

class ExciseController extends AbstractController
{
    /**
     * Рендер React-приложения
     *
     * @return string
     * @throws HttpException
     * @throws \Exception
     */
    public function actionIndex(): string
    {
        GlobalSettingsService::access();
        $this->error404Handler = 'error404Card';
        $this->setFirmParams();
        $id = Yii::$app->request->get('id', 0);
        $this->setPageTitle([['title' => 'Акциз', 'id' => $id]]);
        return $this->renderReact(ExciseService::get($id));
    }
}
