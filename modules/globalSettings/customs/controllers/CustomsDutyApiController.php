<?php

namespace modules\globalSettings\customs\controllers;

use backend\controllers\ApiController;
use backend\validators\common\ValidatorHelpers;
use modules\globalSettings\customs\services\CustomsDutyService;
use modules\globalSettings\customs\validators\CustomsDutySaveValidator;
use modules\globalSettings\index\services\GlobalSettingsService;
use Yii;
use yii\web\HttpException;

class CustomsDutyApiController extends ApiController
{
    /**
     * Сохранение таможенного сбора
     * @return array
     * @throws \Exception
     */
    public function actionSave(): array
    {
        GlobalSettingsService::access();
        $validator = ValidatorHelpers::createFromPost(CustomsDutySaveValidator::class);
        if (!$validator->validate()) {
            return $this->getReactItrErrors($validator);
        }
        /** @var CustomsDutySaveValidator $validator */
        return CustomsDutyService::save($validator);
    }

    /**
     * Удаление таможенного сбора
     * @return array
     * @throws HttpException
     */
    public function actionDelete(): array
    {
        GlobalSettingsService::access();
        $id = (int) Yii::$app->request->post('id');
        return [
            $id => CustomsDutyService::delete($id),
        ];
    }

    /**
     * Получение списков таможенных сборов
     * @return array
     * @throws \Exception
     */
    public function actionList(): array
    {
        return CustomsDutyService::getList();
    }
}
