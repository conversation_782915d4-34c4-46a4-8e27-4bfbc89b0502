import containerDi from '../../../_common/view/di/container';
import { typesDi } from '../../../_common/view/di/types-di';
import { windowConst } from '../../../_common/view/const';
import { ExciseRepository } from './repositories/ExciseRepository';
import { CustomsCommonPage } from './layout/CustomsCommonPage';
import { ITableHeader, ITableRow } from '../../../_common/view/interfaces';
import { numberFormat } from '../../../_common/view/handlers/functions';
import { ColumnAlignType } from '../../../_common/view/types';

export const ExcisePage = () => {
  const repository: ExciseRepository = containerDi.get<ExciseRepository>(typesDi.ExciseRepository);

  const header: ITableHeader[] = [
    { value: 'Мощность двигателя от, л.с.', width: 200, name: 'power_min' },
    { value: 'Мощность двигателя до (включительно), л.с.', width: 300, name: 'power_max' },
    { value: 'Ставка акциза, руб./л.с.', width: 200, name: 'excise_rate' },
    { value: '', width: 'auto', name: '' },
  ];

  const prepareRows = async (rows) => {
    const result: ITableRow[] = [];
    rows.map((row) =>
      result.push({
        id: row.id,
        columns: [
          { name: 'power_min', value: numberFormat(row.power_min, null, true, 0), align: ColumnAlignType.right },
          { name: 'power_max', value: numberFormat(row.power_max, null, true, 0), align: ColumnAlignType.right },
          { name: 'excise_rate', value: numberFormat(row.excise_rate, null, true, 0), align: ColumnAlignType.right },
          { name: '', value: '' },
        ],
        checkboxSelect: false,
      }),
    );
    return result;
  };

  return (
    <CustomsCommonPage
      title={'Акцизы'}
      action={windowConst.excise}
      header={header}
      prepareRows={prepareRows}
      repository={repository}
    />
  );
};
