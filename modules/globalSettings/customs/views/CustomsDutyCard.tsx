import containerDi from '../../../_common/view/di/container';
import { typesDi } from '../../../_common/view/di/types-di';
import { AsNumberInput, FormGroup } from '../../../_common/view/components';
import React, { useState } from 'react';
import { CustomsDutyRepository } from './repositories/CustomsDutyRepository';
import { MenuCustomsDuty } from './layout/MenuCustomsDuty';
import { CustomsCommonCard } from './layout/CustomsCommonCard';

export const CustomsDutyCard = () => {
  const [model, setModel] = useState((document as any).data);
  const repository: CustomsDutyRepository = containerDi.get<CustomsDutyRepository>(typesDi.CustomsDutyRepository);

  const [id, setId]: [id: number, setId: (v: number) => void] = useState(
    Number(new URLSearchParams(window.location.search).get('id') ?? 0),
  );
  const [readonly, setReadonly] = useState(id > 0);
  const handleChange = ({ name, value }) => {
    setModel((prevState) => ({ ...prevState, [name]: value }));
  };
  return (
    <CustomsCommonCard
      title={'Таможенный сбор'}
      repository={repository}
      model={model}
      setModel={setModel}
      Menu={<MenuCustomsDuty id={id} />}
      id={id}
      setId={setId}
      readonly={readonly}
      setReadonly={setReadonly}
    >
      <div>
        <FormGroup label="Начальная стоимость ТС:">
          <AsNumberInput
            name="car_price_from"
            value={model.car_price_from ?? 0}
            minimumFractionDigits={0}
            readonly={readonly}
            onChange={handleChange}
          />
        </FormGroup>
        <FormGroup label="Конечная стоимость ТС:">
          <AsNumberInput
            name="car_price_to"
            value={model.car_price_to ?? 0}
            minimumFractionDigits={0}
            readonly={readonly}
            onChange={handleChange}
          />
        </FormGroup>
        <FormGroup label="Стоимость оформления:">
          <AsNumberInput
            name="registration_cost"
            value={model.registration_cost ?? 0}
            minimumFractionDigits={0}
            readonly={readonly}
            onChange={handleChange}
          />
        </FormGroup>
      </div>
    </CustomsCommonCard>
  );
};
