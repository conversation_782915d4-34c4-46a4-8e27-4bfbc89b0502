import containerDi from '../../../_common/view/di/container';
import { typesDi } from '../../../_common/view/di/types-di';
import { AsNumberInput, FormGroup, Select } from '../../../_common/view/components';
import { useState } from 'react';
import { RecyclingFeeRepository } from './repositories/RecyclingFeeRepository';
import { BuyerConst, jurStatuses } from '../../../_common/view/const';
import { EngineTypeConst } from '../../../_common/view/const';
import { MenuRecyclingFee } from './layout/MenuRecyclingFee';
import { CheckboxGroup } from '../../../_common/view/components/form/CheckboxGroup';
import { CustomsCommonCard } from './layout/CustomsCommonCard';

export const RecyclingFeeCard = () => {
  const [model, setModel] = useState((document as any).data);
  const repository: RecyclingFeeRepository = containerDi.get<RecyclingFeeRepository>(typesDi.RecyclingFeeRepository);

  const [id, setId]: [id: number, setId: (v: number) => void] = useState(
    Number(new URLSearchParams(window.location.search).get('id')),
  );
  const [readonly, setReadonly] = useState(id > 0);
  const handleChange = ({ name, value }) => {
    setModel((prevState) => ({ ...prevState, [name]: value }));
  };
  return (
    <CustomsCommonCard
      title={'Утилизационный сбор'}
      repository={repository}
      model={model}
      setModel={setModel}
      Menu={<MenuRecyclingFee id={id} />}
      id={id}
      setId={setId}
      readonly={readonly}
      setReadonly={setReadonly}
    >
      <div>
        <ul className="form-columns-2">
          <li>
            <FormGroup label="Покупатель:">
              <Select
                name="buyer"
                defaultValue={jurStatuses[0].key}
                value={model.buyer}
                options={jurStatuses}
                onChange={handleChange}
                disabled={readonly}
              />
            </FormGroup>
          </li>
          <li>
            <FormGroup label="Тип двигателя:">
              <CheckboxGroup
                model={model}
                setModel={setModel}
                name={'engine_type'}
                options={EngineTypeConst}
                readonly={readonly}
              />
            </FormGroup>
          </li>
        </ul>
        <ul className="form-columns-2">
          <li>
            <FormGroup label="Возраст ТС от:">
              <AsNumberInput
                name="car_age_from"
                value={model.car_age_from ?? 0}
                minimumFractionDigits={0}
                onChange={handleChange}
                readonly={readonly}
              />
            </FormGroup>
          </li>
          <li>
            <FormGroup label="Возраст ТС до:">
              <AsNumberInput
                name="car_age_to"
                value={model.car_age_to ?? 0}
                minimumFractionDigits={0}
                onChange={handleChange}
                readonly={readonly}
              />
            </FormGroup>
          </li>
        </ul>
        <ul className="form-columns-2">
          <li>
            <FormGroup label="Объем двигателя от:">
              <AsNumberInput
                name="engine_volume_min"
                value={model.engine_volume_min ?? 0}
                onChange={handleChange}
                minimumFractionDigits={0}
                readonly={readonly}
              />
            </FormGroup>
          </li>
          <li>
            <FormGroup label="Объем двигателя до:">
              <AsNumberInput
                name="engine_volume_max"
                value={model.engine_volume_max ?? 0}
                onChange={handleChange}
                minimumFractionDigits={0}
                readonly={readonly}
              />
            </FormGroup>
          </li>
        </ul>
        <ul className="form-columns-2">
          <li>
            <FormGroup label="Коэффициент:">
              <AsNumberInput
                name="coefficient"
                value={model.coefficient ?? 0}
                onChange={handleChange}
                readonly={readonly}
              />
            </FormGroup>
          </li>
        </ul>
      </div>
    </CustomsCommonCard>
  );
};
