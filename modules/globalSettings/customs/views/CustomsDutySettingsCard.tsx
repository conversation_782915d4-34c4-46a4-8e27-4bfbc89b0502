import containerDi from '../../../_common/view/di/container';
import { typesDi } from '../../../_common/view/di/types-di';
import { errorHandler } from '../../../_common/view/handlers/errorHandler';
import { ContainerForm } from '../../../_common/view/layout';
import { AsNumberInput, DivError, FormGroup, SaveButton } from '../../../_common/view/components';
import { useEffect, useState } from 'react';
import { CustomsDutySettingsRepository } from './repositories/CustomsDutySettingsRepository';
import { ContainerTabPage } from '../../../_common/view/layout';
import { GlobalSettingsMenu } from '../../index/views/layout/GlobalSettingsMenu';
import { getTitle } from '../../../_common/view/handlers/functions';

export const CustomsDutySettingsCard = () => {
  const [model, setModel] = useState((document as any).data);
  const [userErrors, setErrors] = useState({});
  const [isLoading, setIsLoading] = useState(false);
  const access = (document as any).access;
  const repository: CustomsDutySettingsRepository = containerDi.get<CustomsDutySettingsRepository>(
    typesDi.CustomsDutySettingsRepository,
  );
  const save = async (e) => {
    e.preventDefault();
    setIsLoading(true);
    try {
      await repository.saveCard(model).then((data) => {
        setModel((prevState) => ({ ...prevState, id: data.id }));
      });
      setIsLoading(false);
    } catch (errors) {
      errorHandler(errors, setErrors);
      setIsLoading(false);
    }
  };

  const handleChange = ({ name, value }) => {
    setModel((prevState) => ({ ...prevState, [name]: value }));
  };

  useEffect(() => {
    document.title = getTitle('Настройки');
  });

  return (
    <>
      <ContainerTabPage>
        <GlobalSettingsMenu />
        <ContainerForm>
          <div className="vspan0 scroll-y scroll-padding">
            <div className="form-horizontal label-large">
              <ul className="form-columns-2">
                <li>
                  <FormGroup label="Базовая ставка утилизационного сбора, руб.:">
                    <AsNumberInput
                      name="util_base_rate"
                      value={model.util_base_rate ?? 0}
                      readonly={false}
                      onChange={handleChange}
                    />
                  </FormGroup>
                </li>
              </ul>
            </div>
          </div>
          <div className="buttons">
            <SaveButton access={true} readonly={false} setReadonly={() => {}} onClick={save} isLoading={isLoading} />
          </div>
        </ContainerForm>
      </ContainerTabPage>
      <DivError errors={Object.values(userErrors)} setErrors={setErrors} />
    </>
  );
};
