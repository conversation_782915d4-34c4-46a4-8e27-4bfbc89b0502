import containerDi from '../../../_common/view/di/container';
import { typesDi } from '../../../_common/view/di/types-di';
import { AsNumberInput, FormGroup, Select } from '../../../_common/view/components';
import { useState } from 'react';
import { BuyerConst } from '../../../_common/view/const';
import { EngineTypeConst } from '../../../_common/view/const';
import { MenuCustomsCollection } from './layout/MenuCustomsCollection';
import { CheckboxGroup } from '../../../_common/view/components/form/CheckboxGroup';
import { CustomsCommonCard } from './layout/CustomsCommonCard';
import { CustomsCollectionRepository } from './repositories/CustomsCollectionRepository';

export const CustomsCollectionCard = () => {
  const [model, setModel] = useState((document as any).data);
  const repository: CustomsCollectionRepository = containerDi.get<CustomsCollectionRepository>(
    typesDi.CustomsCollectionRepository,
  );

  const [id, setId]: [id: number, setId: (v: number) => void] = useState(
    Number(new URLSearchParams(window.location.search).get('id')),
  );
  const [readonly, setReadonly] = useState(id > 0);
  const handleChange = ({ name, value }) => {
    setModel((prevState) => ({ ...prevState, [name]: value }));
  };
  return (
    <CustomsCommonCard
      title={'Таможенная пошлина'}
      repository={repository}
      model={model}
      setModel={setModel}
      Menu={<MenuCustomsCollection id={id} />}
      id={id}
      setId={setId}
      readonly={readonly}
      setReadonly={setReadonly}
    >
      <div>
        <ul className="form-columns-2">
          <li>
            <FormGroup label="Покупатель:">
              <Select
                name="buyer"
                value={model.buyer}
                options={BuyerConst}
                onChange={handleChange}
                disabled={readonly}
              />
            </FormGroup>
          </li>
          <li>
            <FormGroup label="Тип двигателя:">
              <CheckboxGroup
                model={model}
                setModel={setModel}
                name={'engine_type'}
                options={EngineTypeConst}
                readonly={readonly}
              />
            </FormGroup>
          </li>
        </ul>
        <ul className="form-columns-2">
          <li>
            <FormGroup label="Цена ТС от:">
              <AsNumberInput
                name="car_price_from"
                value={model.car_price_from ?? 0}
                minimumFractionDigits={0}
                onChange={handleChange}
                readonly={readonly}
              />
            </FormGroup>
          </li>
          <li>
            <FormGroup label="Цена ТС до:">
              <AsNumberInput
                name="car_price_to"
                value={model.car_price_to ?? 0}
                minimumFractionDigits={0}
                onChange={handleChange}
                readonly={readonly}
              />
            </FormGroup>
          </li>
        </ul>
        <ul className="form-columns-2">
          <li>
            <FormGroup label="Возраст ТС от:">
              <AsNumberInput
                name="car_age_from"
                value={model.car_age_from ?? 0}
                minimumFractionDigits={0}
                onChange={handleChange}
                readonly={readonly}
              />
            </FormGroup>
          </li>
          <li>
            <FormGroup label="Возраст ТС до:">
              <AsNumberInput
                name="car_age_to"
                value={model.car_age_to ?? 0}
                minimumFractionDigits={0}
                onChange={handleChange}
                readonly={readonly}
              />
            </FormGroup>
          </li>
        </ul>
        <ul className="form-columns-2">
          <li>
            <FormGroup label="Объем двигателя от:">
              <AsNumberInput
                name="engine_volume_min"
                value={model.engine_volume_min ?? 0}
                onChange={handleChange}
                minimumFractionDigits={0}
                readonly={readonly}
              />
            </FormGroup>
          </li>
          <li>
            <FormGroup label="Объем двигателя до:">
              <AsNumberInput
                name="engine_volume_max"
                value={model.engine_volume_max ?? 0}
                onChange={handleChange}
                minimumFractionDigits={0}
                readonly={readonly}
              />
            </FormGroup>
          </li>
          <li>
            <FormGroup label="Ставка %:">
              <AsNumberInput
                name="percent_rate"
                value={model.percent_rate ?? 0}
                onChange={handleChange}
                minimumFractionDigits={1}
                readonly={readonly}
              />
            </FormGroup>
          </li>
          <li>
            <FormGroup label="Ставка:">
              <AsNumberInput name="rate" value={model.rate ?? 0} onChange={handleChange} readonly={readonly} />
            </FormGroup>
          </li>
        </ul>
      </div>
    </CustomsCommonCard>
  );
};
