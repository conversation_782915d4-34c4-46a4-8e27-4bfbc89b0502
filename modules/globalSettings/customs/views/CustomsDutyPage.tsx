import { CustomsDutyRepository } from './repositories/CustomsDutyRepository';
import containerDi from '../../../_common/view/di/container';
import { typesDi } from '../../../_common/view/di/types-di';
import { windowConst } from '../../../_common/view/const';
import { CustomsCommonPage } from './layout/CustomsCommonPage';
import { ITableHeader, ITableRow } from '../../../_common/view/interfaces';
import { numberFormat } from '../../../_common/view/handlers/functions';
import { ColumnAlignType } from '../../../_common/view/types';

export const CustomsDutyPage = () => {
  const repository: CustomsDutyRepository = containerDi.get<CustomsDutyRepository>(typesDi.CustomsDutyRepository);
  const header: ITableHeader[] = [
    { value: 'Стоимость автомобиля от, руб.', width: 200, name: 'price_car_from' },
    { value: 'Стоимость автомобиля до (включительно), руб.', width: 300, name: 'price_car_to_include' },
    { value: 'Стоимость оформления, руб.', width: 200, name: 'registration_cost' },
    { value: '', width: 'auto', name: '' },
  ];

  const prepareRows = async (rows) => {
    const result: ITableRow[] = [];
    rows.map((row) =>
      result.push({
        id: row.id,
        columns: [
          {
            name: 'car_price_from',
            value: numberFormat(row.car_price_from, null, true, 0),
            align: ColumnAlignType.right,
          },
          { name: 'car_price_to', value: numberFormat(row.car_price_to, null, true, 0), align: ColumnAlignType.right },
          {
            name: 'registration_cost',
            value: numberFormat(row.registration_cost, null, true, 0),
            align: ColumnAlignType.right,
          },
          { name: '', value: '' },
        ],
        checkboxSelect: false,
      }),
    );
    return result;
  };

  return (
    <CustomsCommonPage
      title={'Таможенный сбор'}
      action={windowConst.custom_duty}
      header={header}
      prepareRows={prepareRows}
      repository={repository}
    />
  );
};
