import containerDi from '../../../_common/view/di/container';
import { typesDi } from '../../../_common/view/di/types-di';
import { AsNumberInput, FormGroup } from '../../../_common/view/components';
import { useState } from 'react';
import { CustomsCommonCard } from './layout/CustomsCommonCard';
import { MenuExcise } from './layout/MenuExcise';
import { ExciseRepository } from './repositories/ExciseRepository';

export const ExciseCard = () => {
  const [model, setModel] = useState((document as any).data);
  const repository: ExciseRepository = containerDi.get<ExciseRepository>(typesDi.ExciseRepository);

  const [id, setId]: [id: number, setId: (v: number) => void] = useState(
    Number(new URLSearchParams(window.location.search).get('id')),
  );
  const [readonly, setReadonly] = useState(id > 0);
  const handleChange = ({ name, value }) => {
    setModel((prevState) => ({ ...prevState, [name]: value }));
  };
  return (
    <CustomsCommonCard
      title={'Акциз'}
      repository={repository}
      model={model}
      setModel={setModel}
      Menu={<MenuExcise id={id} />}
      id={id}
      setId={setId}
      readonly={readonly}
      setReadonly={setReadonly}
    >
      <div>
        <FormGroup label="Мощность от, л.с.:">
          <AsNumberInput
            name="power_min"
            value={model.power_min ?? 0}
            minimumFractionDigits={0}
            readonly={readonly}
            onChange={handleChange}
          />
        </FormGroup>
        <FormGroup label="Мощность до, л.с.:">
          <AsNumberInput
            name="power_max"
            value={model.power_max ?? 0}
            minimumFractionDigits={0}
            readonly={readonly}
            onChange={handleChange}
          />
        </FormGroup>
        <FormGroup label="Ставка акциза, руб./л.с.:">
          <AsNumberInput
            name="excise_rate"
            value={model.excise_rate ?? 0}
            minimumFractionDigits={0}
            readonly={readonly}
            onChange={handleChange}
          />
        </FormGroup>
      </div>
    </CustomsCommonCard>
  );
};
