import containerDi from '../../../_common/view/di/container';
import { typesDi } from '../../../_common/view/di/types-di';
import { EngineTypeConst, jurStatuses, windowConst } from '../../../_common/view/const';
import { CustomsCollectionRepository } from './repositories/CustomsCollectionRepository';
import { CustomsCommonPage } from './layout/CustomsCommonPage';
import { ITableHeader, ITableRow } from '../../../_common/view/interfaces';
import { numberFormat } from '../../../_common/view/handlers/functions';
import { ColumnAlignType } from '../../../_common/view/types';

export const CustomsCollectionPage = () => {
  const repository: CustomsCollectionRepository = containerDi.get<CustomsCollectionRepository>(
    typesDi.CustomsCollectionRepository,
  );
  const header: ITableHeader[] = [
    { value: 'Покупатель', width: 200, name: 'buyer' },
    { value: 'Цена ТС от', width: 200, name: 'car_price_from' },
    { value: 'Цена ТС до', width: 200, name: 'car_price_to' },
    { value: 'Возраст ТС от, лет', width: 200, name: 'car_age_from' },
    { value: 'Возраст ТС до (включительно), лет', width: 200, name: 'car_age_to' },
    { value: 'Объем двигателя от, см3', width: 200, name: 'engine_volume_min' },
    { value: 'Объем двигателя до (включительно), см3', width: 200, name: 'engine_volume_max' },
    { value: 'Тип двигателя', width: 200, name: 'engine_type' },
    { value: 'Ставка, %', width: 200, name: 'percent_rate' },
    { value: 'Ставка, евро/см3 (не менее)', width: 200, name: 'rate' },
  ];

  const prepareRows = async (rows) => {
    const result: ITableRow[] = [];
    rows.map((row) =>
      result.push({
        id: row.id,
        columns: [
          { name: 'buyer', value: jurStatuses.find((item): boolean => item.key == row.buyer)?.value },
          {
            name: 'car_price_from',
            value: numberFormat(row.car_price_from, null, true, 0),
            align: ColumnAlignType.right,
          },
          { name: 'car_price_to', value: numberFormat(row.car_price_to, null, true, 0), align: ColumnAlignType.right },
          { name: 'car_age_from', value: numberFormat(row.car_age_from, null, true, 0), align: ColumnAlignType.right },
          { name: 'car_age_to', value: numberFormat(row.car_age_to, null, true, 0), align: ColumnAlignType.right },
          {
            name: 'engine_volume_min',
            value: numberFormat(row.engine_volume_min, null, true, 0),
            align: ColumnAlignType.right,
          },
          {
            name: 'engine_volume_max',
            value: numberFormat(row.engine_volume_max, null, true, 0),
            align: ColumnAlignType.right,
          },
          {
            name: 'engine_type',
            value: row.engine_type.map((item) => {
              return EngineTypeConst.find((type) => type.key == item)?.value + '\n';
            }),
          },
          { name: 'percent_rate', value: numberFormat(row.percent_rate, null, true, 1), align: ColumnAlignType.right },
          { name: 'rate', value: numberFormat(row.rate, null, true, 1), align: ColumnAlignType.right },
        ],
        checkboxSelect: false,
      }),
    );
    return result;
  };
  return (
    <CustomsCommonPage
      title={'Таможенные пошлины'}
      action={windowConst.customs_collection}
      header={header}
      prepareRows={prepareRows}
      repository={repository}
    />
  );
};
