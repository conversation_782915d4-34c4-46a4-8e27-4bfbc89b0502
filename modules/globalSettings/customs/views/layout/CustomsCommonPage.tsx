import { errorHandler } from '../../../../_common/view/handlers/errorHandler';
import { UpdateButton } from '../../../../_common/view/layout/buttons/UpdateButton';
import { AddButton } from '../../../../_common/view/layout/buttons/AddButton';
import { DivError, Divider } from '../../../../_common/view/components';
import React, { useEffect, useState } from 'react';
import { ICustomsRepository } from '../interfaces/ICustomsRepository';
import { CopyButton } from '../../../../_common/view/layout/buttons/CopyButton';
import { Alert } from '../../../../_common/view/components/popup/Alert';
import { ITableHeader, ITableRow } from '../../../../_common/view/interfaces';
import { ContainerTabPage } from '../../../../_common/view/layout';
import { DataGrid } from '../../../../_common/view/layout/DataGrid';
import { GlobalSettingsMenu } from '../../../index/views/layout/GlobalSettingsMenu';
import { getTitle } from '../../../../_common/view/handlers/functions';

interface CustomsCommonPageProps {
  repository: ICustomsRepository;
  canCopy?: boolean;
  title: string;
  header: ITableHeader[];
  prepareRows(rows: []): Promise<any>;
  action(id): Window;
  copyAction?(id): Window | null;
  checkboxes?: boolean;
}

export const CustomsCommonPage = ({
  repository,
  title,
  header,
  prepareRows,
  action,
  canCopy = false,
  copyAction = null,
}: CustomsCommonPageProps) => {
  const [rows, setRows]: [ITableRow[], any] = useState([]);
  const [isLoading, setIsLoading] = useState(false);
  const [count, setCount] = useState(0);
  const [userErrors, setErrors] = useState({});
  const [selectedRow, setSelectedRow] = useState(0);
  const [alertMessage, setAlertMessage] = useState('');

  const getTable = async () => {
    try {
      setIsLoading(true);
      const data = await repository.getTable();
      const rows = await prepareRows(data.list);
      setRows(rows);
      setCount(data.count);
      setIsLoading(false);
    } catch (e) {
      setIsLoading(false);
      errorHandler(e, setErrors);
    }
  };
  const copy = () => {
    if (canCopy) {
      if (!selectedRow) {
        setAlertMessage('Выберите запись утилизационного сбора');
        return;
      }
      copyAction(selectedRow);
    }
  };
  useEffect(() => {
    document.title = getTitle(title);
    getTable();
    (window as any).getTable = getTable;
  }, []);

  return (
    <>
      <ContainerTabPage>
        <GlobalSettingsMenu />

        <div className="vspan0 container-form">
          <div className="container-wrap">
            <div className="container">
              <ul className="toolbar">
                <li>
                  <UpdateButton onClick={getTable} />
                </li>
                <li>
                  <AddButton window={action} />
                </li>
                {canCopy && (
                  <>
                    <li>
                      <Divider />
                    </li>
                    <li>
                      <CopyButton onClick={copy} />
                    </li>
                  </>
                )}
              </ul>
              <div className="vspan0">
                <DataGrid
                  onDoubleClick={action}
                  windowParams={[]}
                  rows={rows}
                  setRows={setRows}
                  header={header}
                  footer={{ count: count }}
                  isLoading={isLoading}
                  checkboxes={false}
                  onRowSelect={setSelectedRow}
                  resizeable={false}
                />
              </div>
            </div>
          </div>
        </div>
        {alertMessage && (
          <Alert
            message={alertMessage}
            title={''}
            onClickOk={() => {
              setAlertMessage('');
            }}
          />
        )}
      </ContainerTabPage>
      <DivError errors={Object.values(userErrors)} setErrors={setErrors} />
    </>
  );
};
