import { IconConst } from '../../../../_common/view/const';
import { IMenuItem } from '../../../../_common/view/interfaces';
import { MenuHorizontal } from '../../../../_common/view/menu';

export const MenuSettings = () => {
  const menuItems: Array<IMenuItem> = [
    {
      icon: IconConst.settings,
      title: 'Настройки',
      href: ``,
      id: 'settings',
      class: 'active',
    },
  ];

  return <MenuHorizontal items={menuItems} />;
};
