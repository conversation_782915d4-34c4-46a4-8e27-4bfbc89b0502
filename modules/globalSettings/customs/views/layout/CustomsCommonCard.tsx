import { errorHandler } from '../../../../_common/view/handlers/errorHandler';
import { ContainerForm, ContainerPage } from '../../../../_common/view/layout';
import { CloseButton, DeleteButton, DivError, SaveButton } from '../../../../_common/view/components';
import { useEffect, useState } from 'react';
import { ICustomsRepository } from '../interfaces/ICustomsRepository';
import { getTitle } from '../../../../_common/view/handlers/functions';
import { JSX } from 'react';

interface CustomsCommonCardProps {
  title: string;
  model: {};
  setModel: any;
  id: number;
  setId: (v: number) => void;
  readonly: boolean;
  setReadonly: (v: boolean) => void;
  repository: ICustomsRepository;
  Menu: JSX.Element;
  children?: JSX.Element | JSX.Element[];
}

export const CustomsCommonCard = ({
  title,
  model,
  setModel,
  repository,
  Menu,
  children,
  id,
  setId,
  readonly,
  setReadonly,
}: CustomsCommonCardProps) => {
  const [userErrors, setErrors] = useState({});
  const [defaultModel, setDefaultModel] = useState({ ...model });
  const [isLoading, setIsLoading] = useState(false);

  useEffect(() => {
    document.title = getTitle(id > 0 ? `${title} ${id}` : title);
  }, []);

  const save = async (e) => {
    e.preventDefault();
    setIsLoading(true);
    try {
      await repository.saveCard(model).then((data) => {
        if (!id) {
          window.history.pushState('', '', `?id=${data.id}`);
          setId(data.id);
          setModel((prevState) => ({ ...prevState, id: data.id }));
        }
        setReadonly(true);
        setErrors({});
        setDefaultModel(model);
        window?.opener?.getTable();
        setIsLoading(false);
      });
    } catch (errors) {
      errorHandler(errors, setErrors);
      setIsLoading(false);
    }
  };

  const deleteCard = async (e) => {
    e.preventDefault();
    setIsLoading(true);
    try {
      await repository.deleteCard(id);
      window?.opener?.getTable();
      window.close();
    } catch (errors) {
      errorHandler(errors, setErrors);
      setIsLoading(false);
    }
  };
  return (
    <>
      <ContainerPage>
        {Menu}
        <ContainerForm>
          <div className="vspan0 scroll-y scroll-padding">
            <div className="form-horizontal label-medium">{children}</div>
          </div>
          <div className="buttons">
            <div className="pull-left">
              <DeleteButton access={true} readonly={id} onClick={deleteCard} />
            </div>
            <CloseButton
              readonly={readonly || id == 0}
              setReadonly={setReadonly}
              setDefaultModel={setModel}
              defaultModel={defaultModel}
              isLoading={isLoading}
            />
            <SaveButton
              access={true}
              readonly={readonly}
              setReadonly={setReadonly}
              onClick={save}
              isLoading={isLoading}
            />
          </div>
        </ContainerForm>
      </ContainerPage>
      <DivError errors={Object.values(userErrors)} setErrors={setErrors} />
    </>
  );
};
