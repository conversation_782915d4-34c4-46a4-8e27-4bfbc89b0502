import { IconConst } from '../../../../_common/view/const';
import { legacyUrl } from '../../../../../conf';
import { IMenu } from '../interfaces/IMenu';
import { IMenuItem } from '../../../../_common/view/interfaces';
import { MenuHorizontal } from '../../../../_common/view/menu';

export const MenuCustomsCollection = ({ id }: IMenu) => {
  const menuItems: Array<IMenuItem> = [
    {
      icon: IconConst.money,
      title: 'Таможенная пошлина',
      href: ``,
      id: 'customs_collection',
      class: 'active',
    },
  ];

  if (id) {
    menuItems.push({
      icon: IconConst.changes,
      title: 'Правки',
      href: legacyUrl + `/global-settings/customs/customs-collection/access`,
      params: { id: id },
      id: 'access',
      class: '',
    });
  }

  return <MenuHorizontal items={menuItems} />;
};
