import { MenuHorizontal } from '../../../../_common/view/menu';
import { IconConst } from '../../../../_common/view/const';
import { legacyUrl } from '../../../../../conf';
import { IMenu } from '../interfaces/IMenu';
import { IMenuItem } from '../../../../_common/view/interfaces';

export const MenuRecyclingFee = ({ id }: IMenu) => {
  const menuItems: Array<IMenuItem> = [
    {
      icon: IconConst.coins,
      title: 'Утилизационный сбор',
      href: ``,
      id: 'custom_duty',
      class: 'active',
    },
  ];
  if (id) {
    menuItems.push({
      icon: IconConst.changes,
      title: 'Правки',
      href: legacyUrl + `/global-settings/customs/recycling-fee/access`,
      params: { id: id },
      id: 'access',
      class: '',
    });
  }

  return <MenuHorizontal items={menuItems} />;
};
