import { inject, injectable } from 'inversify';
import { typesDi } from '../../../../_common/view/di/types-di';
import { HttpService } from '../../../../_common/view/services';
import { ICustomsRepository } from '../interfaces/ICustomsRepository';

@injectable()
export class CustomsDutySettingsRepository implements ICustomsRepository {
  constructor(@inject(typesDi.HttpService) private httpService: HttpService) {}

  async saveCard(data: any) {
    return await this.httpService.post('/global-settings/customs/customs-duty-settings-api/save/', data);
  }
}
