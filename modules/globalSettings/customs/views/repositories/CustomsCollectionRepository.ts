import { inject, injectable } from 'inversify';
import { typesDi } from '../../../../_common/view/di/types-di';
import { HttpService } from '../../../../_common/view/services';
import { ICustomsRepository } from '../interfaces/ICustomsRepository';

@injectable()
export class CustomsCollectionRepository implements ICustomsRepository {
  constructor(@inject(typesDi.HttpService) private httpService: HttpService) {}

  async saveCard(data: any) {
    return await this.httpService.post('/global-settings/customs/customs-collection-api/save/', data);
  }
  async deleteCard(id: number) {
    return await this.httpService.post('/global-settings/customs/customs-collection-api/delete/', { id: id });
  }
  async getTable() {
    return await this.httpService.post('/global-settings/customs/customs-collection-api/list/', {});
  }
}
