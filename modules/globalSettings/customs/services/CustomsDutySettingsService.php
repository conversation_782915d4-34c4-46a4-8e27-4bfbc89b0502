<?php

namespace modules\globalSettings\customs\services;

use common\models\CustomsDutySettingsModel;
use Exception;
use modules\globalSettings\customs\validators\CustomsDutySettingsValidator;

class CustomsDutySettingsService
{

    /**
     * Получить запись настроек
     * @return array
     * @throws Exception
     */
    public static function get(): array
    {
        $setting = CustomsDutySettingsModel::find()->limit(1)->one();
        if ($setting) {
            return $setting->toArray();
        }
        return [];
    }

    /**
     * Сохранение настроек
     * @param CustomsDutySettingsValidator $validator
     * @return array
     * @throws Exception
     */
    public static function save(CustomsDutySettingsValidator $validator): array
    {
        $setting = CustomsDutySettingsModel::find()->one();
        if(!$setting) {
            $setting = new CustomsDutySettingsModel();
        }
        $setting->util_base_rate = $validator->util_base_rate;
        $setting->save();
        return self::get();
    }
}