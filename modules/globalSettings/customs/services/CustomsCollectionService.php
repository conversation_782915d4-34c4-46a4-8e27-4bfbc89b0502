<?php

namespace modules\globalSettings\customs\services;

use common\models\CustomsCollectionModel;
use Exception;
use modules\globalSettings\customs\validators\CustomsCollectionSaveValidator;
use Yii;
use yii\web\NotFoundHttpException;

class CustomsCollectionService extends CustomsService
{
    /**
     * Получить список записей
     * @return array
     * @throws Exception
     */
    public static function getList(): array
    {
        $list = CustomsCollectionModel::find()->all();
        foreach ($list as $key => $item) {
            $item['engine_type'] = json_decode($item['engine_type'], true);
            $list[$key] = $item;
        }
        return [
            'list' => $list,
            'count' => count($list),
        ];
    }

    /**
     * Получить запись таможенного сбора
     * @param int|null $id
     * @return array
     * @throws Exception
     */
    public static function get(int|null $id): array
    {
        if ($id) {
            $customsCollection = CustomsCollectionModel::findOne(['id' => $id]);
            if (!$customsCollection) {
                Yii::$app->response->redirect(
                    getenv('ENV_LEGACY_URL') . "/global-settings/customs/customs-collection/access?id=$id",
                );
                return [];
            }
            $res = $customsCollection->toArray();
            $res['engine_type'] = $customsCollection->getEngineTypes();
            return $res;
        }
        return [
            'engine_type' => [],
        ];
    }

    /**
     * Сохранение таможенного сбора
     * @param CustomsCollectionSaveValidator $validator
     * @return array
     * @throws Exception
     */
    public static function save(CustomsCollectionSaveValidator $validator): array
    {
        if ($validator->id) {
            $customsCollection = CustomsCollectionModel::findOne(['id' => $validator->id]);
        } else {
            $customsCollection = new CustomsCollectionModel();
        }
        self::saveModel($customsCollection, $validator);
        return self::get($customsCollection->id);
    }

    /**
     * Удаление таможенного сбора
     * @param int $id
     * @return int
     * @throws Exception
     */
    public static function delete(int $id): int
    {
        $customsCollection = CustomsCollectionModel::findOne(['id' => $id]);
        $customsCollection?->delete();
        return $id;
    }
}
