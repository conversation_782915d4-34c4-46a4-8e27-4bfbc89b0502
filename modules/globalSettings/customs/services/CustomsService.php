<?php

namespace modules\globalSettings\customs\services;

use common\models\interfaces\ICustoms;
use modules\globalSettings\customs\validators\abstractValidator\CustomsValidator;
use modules\globalSettings\customs\validators\RecyclingFeeSaveValidator;

class CustomsService
{
    /**
     * Сохранение
     * @param ICustoms $model
     * @param RecyclingFeeSaveValidator $validator
     * @return void
     */
    public static function saveModel(ICustoms $model, CustomsValidator $validator): void
    {
        $model->setAttributes($validator->attributes, false);
        $model->save();
    }
}
