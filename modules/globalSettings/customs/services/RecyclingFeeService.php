<?php

namespace modules\globalSettings\customs\services;

use common\models\RecyclingFeeModel;
use Exception;
use modules\globalSettings\customs\validators\RecyclingFeeSaveValidator;
use Throwable;
use Yii;
use yii\db\StaleObjectException;
use yii\web\NotFoundHttpException;

class RecyclingFeeService extends CustomsService
{
    /**
     * Получение записи утилизационного сбора
     * @param int|null $id
     * @return array
     * @throws Exception
     */
    public static function get(int|null $id): array
    {
        if ($id) {
            $recyclingFee = RecyclingFeeModel::findOne(['id' => $id]);
            if (!$recyclingFee) {
                Yii::$app->response->redirect(
                    getenv('ENV_LEGACY_URL') . "/global-settings/customs/recycling-fee/access?id=$id",
                );
                return [];
            }
            $res = $recyclingFee->toArray();
            $res['engine_type'] = $recyclingFee->getEngineTypes();
            return $res;
        }
        return [
            'engine_type' => [],
        ];
    }

    /**
     * Получение списка записей утилизационного сбора
     * @return array
     * @throws Exception
     */
    public static function getList(): array
    {
        $list = RecyclingFeeModel::find()->all();
        foreach ($list as $key => $item) {
            $item['engine_type'] = json_decode($item['engine_type'], true);
            $list[$key] = $item;
        }
        return [
            'list' => $list,
            'count' => count($list),
        ];
    }

    /**
     * Сохранение утилизационного сбора
     * @param RecyclingFeeSaveValidator $validator
     * @return array
     * @throws Exception
     */
    public static function save(RecyclingFeeSaveValidator $validator): array
    {
        if ($validator->id) {
            $recyclingFee = RecyclingFeeModel::findOne(['id' => $validator->id]);
        } else {
            $recyclingFee = new RecyclingFeeModel();
        }
        self::saveModel($recyclingFee, $validator);
        return self::get($recyclingFee->id);
    }

    /**
     * Удаление утилизационного сбора
     * @param int $id
     * @return int
     * @throws Throwable
     * @throws StaleObjectException
     */
    public static function delete(int $id): int
    {
        $customDuty = RecyclingFeeModel::findOne(['id' => $id]);
        $customDuty?->delete();
        return $id;
    }
}
