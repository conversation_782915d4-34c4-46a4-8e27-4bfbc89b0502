<?php

namespace modules\globalSettings\customs\services;

use common\models\CustomsDutyModel;
use Exception;
use modules\globalSettings\customs\validators\CustomsDutySaveValidator;
use Yii;
use yii\web\NotFoundHttpException;

class CustomsDutyService extends CustomsService
{
    /**
     * Получить список записей
     * @return array
     * @throws Exception
     */
    public static function getList(): array
    {
        $list = CustomsDutyModel::find()->all();

        return [
            'list' => $list,
            'count' => count($list),
        ];
    }

    /**
     * Получить запись таможенного сбора
     *
     * @param int|null $id
     *
     * @return array
     * @throws Exception
     */
    public static function get(int|null $id): array
    {
        if ($id) {
            $customsDuty = CustomsDutyModel::findOne(['id' => $id]);
            if (!$customsDuty) {
                Yii::$app->response->redirect(
                    getenv('ENV_LEGACY_URL') . "/global-settings/customs/customs-duty/access?id=$id",
                );
                return [];
            }

            return $customsDuty->toArray();
        }

        return [];
    }

    /**
     * Сохранение таможенного сбора
     *
     * @param CustomsDutySaveValidator $validator
     *
     * @return array
     * @throws Exception
     */
    public static function save(CustomsDutySaveValidator $validator): array
    {
        if ($validator->id) {
            $customDuty = CustomsDutyModel::findOne(['id' => $validator->id]);
        } else {
            $customDuty = new CustomsDutyModel();
        }
        self::saveModel($customDuty, $validator);

        return self::get($customDuty->id);
    }

    /**
     * Удаление таможенного сбора
     *
     * @param int $id
     *
     * @return int
     * @throws Exception
     */
    public static function delete(int $id): int
    {
        $customDuty = CustomsDutyModel::findOne(['id' => $id]);
        $customDuty?->delete();

        return $id;
    }
}
