<?php

namespace modules\globalSettings\customs\services;

use common\models\ExciseModel;
use Exception;
use modules\globalSettings\customs\validators\ExciseSaveValidator;
use Throwable;
use Yii;
use yii\db\StaleObjectException;
use yii\web\NotFoundHttpException;

class ExciseService extends CustomsService
{
    /**
     * Получение записи акциза
     *
     * @param int|null $id
     *
     * @return array
     * @throws Exception
     */
    public static function get(int|null $id): array
    {
        if ($id) {
            $excise = ExciseModel::findOne(['id' => $id]);
            if (!$excise) {
                Yii::$app->response->redirect(
                    getenv('ENV_LEGACY_URL') . "/global-settings/customs/excise/access?id=$id",
                );
                return [];
            }

            return $excise->toArray();
        }

        return [];
    }

    /**
     * Получение списка записей акциз
     * @return array
     * @throws Exception
     */
    public static function getList(): array
    {
        $list = ExciseModel::find()->all();

        return [
            'list' => $list,
            'count' => count($list),
        ];
    }

    /**
     * Сохранение акциз
     *
     * @param ExciseSaveValidator $validator
     *
     * @return array
     * @throws Exception
     */
    public static function save(ExciseSaveValidator $validator): array
    {
        if ($validator->id) {
            $excise = ExciseModel::findOne(['id' => $validator->id]);
        } else {
            $excise = new ExciseModel();
        }
        self::saveModel($excise, $validator);

        return self::get($excise->id);
    }

    /**
     * Удаление утилизационного сбора
     *
     * @param int $id
     *
     * @return int
     * @throws Throwable
     * @throws StaleObjectException
     */
    public static function delete(int $id): int
    {
        $excise = ExciseModel::findOne(['id' => $id]);
        $excise->delete();

        return $id;
    }
}
