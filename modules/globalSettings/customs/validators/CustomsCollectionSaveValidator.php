<?php

namespace modules\globalSettings\customs\validators;

use backend\filters\request\ArrayFilter;
use backend\filters\request\FloatFilter;
use backend\validators\common\ArrayValidator;
use backend\validators\common\NumberValidator;
use modules\globalSettings\customs\validators\abstractValidator\CustomsValidator;

class CustomsCollectionSaveValidator extends CustomsValidator
{
    public $id;
    public $buyer;
    public $car_age_from;
    public $car_age_to;
    public $car_price_from;
    public $car_price_to;
    public $engine_volume_min;
    public $engine_volume_max;
    public $engine_type;
    public $percent_rate;
    public $rate;

    public function rules(): array
    {
        return [
            [['engine_type', 'buyer'], 'required'],
            [['engine_type'], ArrayValidator::class],
            [['engine_type'], ArrayFilter::class],
            [['car_age_from', 'car_age_to', 'car_price_from', 'car_price_to', 'engine_volume_min', 'engine_volume_max', 'percent_rate', 'rate'], 'default', 'value' => 0],
            [['car_age_from', 'car_age_to', 'car_price_from', 'car_price_to', 'engine_volume_min', 'engine_volume_max', 'rate', 'percent_rate'], FloatFilter::class],
            [['car_age_from', 'car_age_to', 'car_price_from', 'car_price_to', 'engine_volume_min', 'engine_volume_max', 'rate', 'percent_rate'], NumberValidator::class,'min' => 0],
            [
                ['engine_volume_min'],
                function () {
                    if ($this->engine_volume_min > $this->engine_volume_max) {
                        $this->addError('engine_volume_min', '"объем двигателя от" не должен быть больше "объем двигателя до"');
                    }
                },
            ],
            [
                ['car_age_from'],
                function () {
                    if ($this->car_age_from > $this->car_age_to) {
                        $this->addError('car_age_from', '"возраст ТС от" не должен быть больше "возраст ТС до"');
                    }
                },
            ],
            [
                ['car_price_from'],
                function () {
                    if ($this->car_price_from > $this->car_price_to) {
                        $this->addError('car_price_from', '"Цена ТС от" не должна быть больше "Цена ТС до"');
                    }
                },
            ],
        ];
    }

    public function attributeLabels(): array
    {
        return [
            'buyer' => 'Покупатель',
            'car_age_from' => 'Возраст ТС от',
            'car_age_to' => 'Возраст ТС до',
            'engine_volume_min' => 'Объем двигателя, от',
            'engine_volume_max' => 'Объем двигателя, до',
            'engine_type' => 'Тип двигателя',
            'percent_rate' => 'Ставка %',
            'rate' => 'Ставка',
        ];
    }
}
