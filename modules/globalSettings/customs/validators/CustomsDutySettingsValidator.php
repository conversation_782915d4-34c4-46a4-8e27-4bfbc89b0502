<?php

namespace modules\globalSettings\customs\validators;

use backend\filters\request\FloatFilter;
use backend\validators\common\NumberValidator;
use modules\globalSettings\customs\validators\abstractValidator\CustomsValidator;

class CustomsDutySettingsValidator extends CustomsValidator
{
    public $util_base_rate;

    public function rules(): array
    {
        return [
            [['util_base_rate'], 'default', 'value' => 0],
            [['util_base_rate'], FloatFilter::class],
            [['util_base_rate'], NumberValidator::class,'min' => 0],
        ];
    }

    public function attributeLabels(): array
    {
        return [
            'util_base_rate' => 'Базовая ставка утилизационного сбора',
        ];
    }
}