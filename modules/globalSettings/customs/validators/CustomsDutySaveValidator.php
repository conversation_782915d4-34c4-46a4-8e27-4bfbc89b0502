<?php

namespace modules\globalSettings\customs\validators;

use backend\filters\request\FloatFilter;
use backend\validators\common\NumberValidator;
use modules\globalSettings\customs\validators\abstractValidator\CustomsValidator;

class CustomsDutySaveValidator extends CustomsValidator
{
    public $car_price_from;
    public $car_price_to;
    public $registration_cost;

    public function rules(): array
    {
        return [
            [['car_price_from', 'car_price_to', 'registration_cost'], 'default', 'value' => '0'],
            [['car_price_from', 'car_price_to', 'registration_cost'], FloatFilter::class],
            [['car_price_from', 'car_price_to', 'registration_cost'], NumberValidator::class,'min' => 0],
            [
                ['car_price_from'],
                function () {
                    if ($this->car_price_from > $this->car_price_to) {
                        $this->addError('car_price_from', '"Начальная стоимость ТС" не должна быть больше "Конечная стоимость ТС"');
                    }
                },
            ],
        ];
    }

    public function attributeLabels(): array
    {
        return [
            'car_price_from' => 'Начальная стоимость ТС',
            'car_price_to' => 'Конечная стоимость ТС',
            'registration_cost' => 'Стоимость оформления',
        ];
    }
}
