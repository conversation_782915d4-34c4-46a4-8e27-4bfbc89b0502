<?php

namespace modules\globalSettings\customs\validators;

use backend\filters\request\FloatFilter;
use backend\validators\common\NumberValidator;
use modules\globalSettings\customs\validators\abstractValidator\CustomsValidator;

class ExciseSaveValidator extends CustomsValidator
{
    public $power_min;
    public $power_max;
    public $excise_rate;

    public function rules(): array
    {
        return [
            [['excise_rate', 'power_min', 'power_max'], 'default', 'value' => 0],
            [['excise_rate', 'power_min', 'power_max'], FloatFilter::class],
            [['excise_rate', 'power_min', 'power_max'], NumberValidator::class, 'min' => 0],
            [
                ['power_min'],
                function () {
                    if ($this->power_min > $this->power_max) {
                        $this->addError('power_min', '"Мощность двигателя от" не должен быть больше "Мощность двигателя до"');
                    }
                },
            ],

        ];
    }
    public function attributeLabels(): array
    {
        return [
            'power_min' => 'Мощность двигателя от',
            'power_max' => 'Мощность двигателя до',
            'excise_rate' => 'Ставка акциза',
        ];
    }
}