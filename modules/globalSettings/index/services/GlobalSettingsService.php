<?php
namespace modules\globalSettings\index\services;
use common\services\AccessService;
use Exception;
use yii\web\HttpException;

class GlobalSettingsService
{
    /**
     * Проверка доступа
     *
     * @throws HttpException
     * @throws Exception
     */
    public static function access(): void
    {
        if (!AccessService::menu('global')) {
            throw new HttpException(403);
        }
    }
}
