import { IMenuItem } from '../../../../_common/view/interfaces';
import { IconConst } from '../../../../_common/view/const';
import { MenuHorizontal, UserRightMenu } from '../../../../_common/view/menu';
import { legacyUrl } from '../../../../../conf';
import { memo } from 'react';

export const GlobalSettingsMenu = memo(() => {
  const menuItems: Array<IMenuItem> = [
    {
      title: 'Валюта',
      icon: IconConst.currency,
      href: '/global-settings/currencies/',
    },
    {
      title: 'Курсы валют',
      icon: IconConst.chartUp,
      href: '/global-settings/exchange-rates/',
    },
    {
      title: 'Таможенное оформление',
      icon: IconConst.bank,
      href: '/global-settings/customs/customs-duty/list/',
      subMenuId: 'customs',
      subMenu: [
        {
          title: 'Таможенный сбор',
          icon: IconConst.money,
          href: '/global-settings/customs/customs-duty/list/',
          subMenuId: 'customs',
        },
        {
          title: 'Утилизационный сбор',
          icon: IconConst.coins,
          href: '/global-settings/customs/recycling-fee/list/',
          subMenuId: 'customs',
        },
        {
          title: 'Акцизы',
          icon: IconConst.chartUp,
          href: '/global-settings/customs/excise/list/',
          subMenuId: 'customs',
        },
        {
          title: 'Таможенные пошлины',
          icon: IconConst.money,
          href: '/global-settings/customs/customs-collection/list/',
          subMenuId: 'customs',
        },
        {
          title: 'Настройки',
          icon: IconConst.settings,
          href: '/global-settings/customs/customs-duty-settings/',
          subMenuId: 'customs',
        },
      ],
    },
    {
      title: 'Транспортный налог',
      icon: IconConst.bank,
      href: legacyUrl + '/global-settings/taxes/',
      subMenuId: 'taxes',
      subMenu: [
        {
          title: 'Налоговые ставки',
          icon: IconConst.calc,
          href: legacyUrl + '/global-settings/taxes/',
          subMenuId: 'taxes',
        },
        {
          icon: IconConst.chartUp,
          title: 'Минпромторг',
          href: legacyUrl + '/global-settings/taxes/tax-multipliers/',
          subMenuId: 'taxes',
        },
        {
          title: 'Налоговые льготы',
          icon: IconConst.calc,
          href: legacyUrl + '/global-settings/taxes/tax-benefits/',
          subMenuId: 'taxes',
        },
        {
          title: 'Настройки',
          icon: IconConst.spare,
          href: legacyUrl + '/global-settings/taxes/tax-settings/',
          subMenuId: 'taxes',
        },
        {
          title: 'Справка',
          icon: IconConst.information,
          href: '/global-settings/taxes/tax-info/',
          subMenuId: 'taxes',
        },
      ],
    },
  ];

  return (
    <MenuHorizontal items={menuItems}>
      <UserRightMenu />
    </MenuHorizontal>
  );
});
