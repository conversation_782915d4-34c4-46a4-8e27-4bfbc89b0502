<?php

namespace modules\globalSettings\exchangeRates\validators;

use backend\validators\common\NumberValidator;
use yii\base\Model;

class SaveExchangeRateValidator extends Model
{
    public $id;
    public $date;
    public $nominal;
    public $value;
    public $num_code;
    public $char_code;
    public $name;
    public $bank;

    public function rules(): array
    {
        return [
            [['id'], 'default', 'value' => 0],
            [['date', 'nominal', 'value', 'num_code', 'char_code', 'name', 'bank'], 'required'],
            [['id', 'nominal', 'value', 'num_code'], NumberValidator::class],
            [['date', 'char_code', 'name', 'bank'], 'string'],
        ];
    }
}
