<?php

namespace modules\globalSettings\exchangeRates\validators;

use modules\globalSettings\currencies\validators\CurrenciesValidator;
use modules\globalSettings\taxes\taxMultipliers\validators\DateValidator;
use yii\base\Model;

class ExchangeRatesValidator extends Model
{
    public $date_from;
    public $date_to;
    public $currencies;

    public function rules(): array
    {
        return [
            [['date_to'], 'default', 'value' => date('d.m.Y')],
            [['date_from', 'date_to'], DateValidator::class],
            [['currencies'], CurrenciesValidator::class],
        ];
    }

    public function attributeLabels(): array
    {
        return [
            'currencies' => 'Валюты',
        ];
    }
}
