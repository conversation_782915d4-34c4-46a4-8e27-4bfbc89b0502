<?php

namespace modules\globalSettings\exchangeRates\services;

use common\data\dataSources\ExchangeRatesDataSource;
use common\models\CurrenciesModel;
use common\models\ExchangeRatesModel;
use common\services\common\StringService;
use common\services\LogService;
use common\types\CurrencyIsoTypes;
use common\types\CurrencyTypes;
use Exception;
use modules\globalSettings\exchangeRates\validators\ExchangeRatesValidator;
use modules\globalSettings\exchangeRates\validators\SaveExchangeRateValidator;
use yii\db\ActiveQuery;
use yii\web\HttpException;

class ExchangeRatesService
{
    private const DEFAULT_CURRENCY_NUM_CODE = CurrencyIsoTypes::RUB;
    private const DEFAULT_CURRENCY_CHAR_CODE = CurrencyTypes::RUB;

    private const CBR = 'ЦБР';
    private const MOEX = 'MOEX';

    /**
     * Возвращает таблицу курсов валют
     * @param ExchangeRatesValidator $validator
     * @return array
     * @throws Exception
     */
    public static function getExchangeRatesTable(ExchangeRatesValidator $validator): array
    {
        if (!$validator->currencies) {
            return [];
        }
        $date = date('Y-m-d');
        $query = self::getExchangeRatesQuery($validator, self::CBR);
        if (!$validator->date_to) {
            $validator->date_to = $date;
        }
        if ($validator->date_from) {
            $query->andWhere(['>=', 'date', $validator->date_from]);
        }
        $rates = $query
            ->andWhere(['<=', 'date', $validator->date_to])
            ->orderBy('date DESC')
            ->all();
        if ($validator->date_to == $date && (!$rates || $rates[0]['date'] != $date)) {
            $moexRate = self::getExchangeRatesQuery($validator, self::MOEX)->all();
            return array_merge($moexRate, $rates);
        }
        return $rates;
    }

    /**
     * Запрос валют, для конкретного банка
     * @param ExchangeRatesValidator $validator
     * @param string $bank
     * @return ActiveQuery
     * @throws Exception
     */
    private static function getExchangeRatesQuery(ExchangeRatesValidator $validator, string $bank): ActiveQuery
    {
        return ExchangeRatesModel::find()
            ->select(['*', 'DATE_FORMAT(date, "%d.%m.%Y") as formatted_date', "REPLACE(value, '.', ',') as value"])
            ->where(['num_code' => $validator->currencies])
            ->andWhere(['bank' => $bank])
            ->asArray();
    }

    /**
     * Сохранить текущий курс валют
     * @throws Exception
     */
    public static function saveCBRExchangeRates(ExchangeRatesValidator $validator): void
    {
        $getCurrentRate = ExchangeRatesModel::find()
            ->where(['date' => $validator->date_to])
            ->andWhere(['bank' => self::CBR])
            ->one();
        if ($getCurrentRate) {
            return;
        }
        $dynamic = ExchangeRatesService::getCBRExchangeRates($validator);
        /** @var SaveExchangeRateValidator $item */
        foreach ($dynamic as $item) {
            if (self::validateExchangeRate($item)) {
                try {
                    self::saveExchangeRate($item);
                } catch (Exception $e) {
                    LogService::error($e);
                }
            }
        }
    }

    /**
     * Получить динамику выбранных валют, за указанный период для ЦБР
     * @param ExchangeRatesValidator $validator
     * @return array
     * @throws Exception
     */
    private static function getCBRExchangeRates(ExchangeRatesValidator $validator): array
    {
        $list = self::getCBRCurrenciesList();
        $result = [];
        foreach ($validator->currencies as $currency) {
            $id = $list[$currency['char_code']]['id'] ?? 0;
            if (!$id) {
                continue;
            }
            $data = ExchangeRatesDataSource::getCBR($id, $validator->date_from, $validator->date_to);
            if ($validator->date_from == $validator->date_to) {
                $value = $data['Record']['Value'] ?? 0;
                $result[] = self::createExchangeRate($currency, $validator->date_to, $value, self::CBR);
            } else {
                foreach ($data['Record'] as $record) {
                    if (is_array($record)) {
                        $date = $record['@attributes']['Date'] ?? '';
                        $value = $record['Value'] ?? 0;
                        $result[] = self::createExchangeRate($currency, $date, $value, self::CBR);
                    }
                }
            }
        }
        return $result;
    }

    /**
     * Получить список всех валют отслеживаемых ЦБР
     * @return array
     * @throws Exception
     */
    private static function getCBRCurrenciesList(): array
    {
        $data = ExchangeRatesDataSource::getCBRCurrenciesList();
        $result = [];
        foreach ($data['Valute'] as $value) {
            $result[$value['CharCode']] = [
                'num_code' => $value['NumCode'],
                'char_code' => $value['CharCode'],
                'name' => $value['Name'],
                'id' => $value['@attributes']['ID'],
            ];
        }
        return $result;
    }

    /**
     * Получить текущий курс валют для MOEX
     * @param ExchangeRatesValidator $validator
     * @return void
     * @throws Exception
     */
    public static function saveMOEXExchangeRates(ExchangeRatesValidator $validator): void
    {
        foreach ($validator->currencies as $currency) {
            $security = "{$currency['char_code']}/" . self::DEFAULT_CURRENCY_CHAR_CODE;
            $data = ExchangeRatesDataSource::getMOEX($security, $validator->date_to);
            $rate = $data['data'][3]['rows']['row']['@attributes']['rate'] ?? 0;
            if (!$rate) {
                continue;
            }
            $newRate = self::createExchangeRate($currency, $validator->date_to, $rate, self::MOEX);
            $newRate->id =
                ExchangeRatesModel::findOne(['char_code' => $currency['char_code'], 'bank' => self::MOEX])?->id ?? 0;
            if (self::validateExchangeRate($newRate)) {
                try {
                    self::saveExchangeRate($newRate);
                } catch (Exception $e) {
                    LogService::error($e);
                }
            }
        }
    }

    /**
     * Создает экземпляр курса валют
     * @param array $currency
     * @param string $date
     * @param string|float|int $value
     * @param string $bank
     * @return SaveExchangeRateValidator
     */
    private static function createExchangeRate(
        array $currency,
        string $date,
        string|float|int $value,
        string $bank,
    ): SaveExchangeRateValidator {
        $validator = new SaveExchangeRateValidator();
        $validator->date = date('Y-m-d', strtotime($date));
        $validator->nominal = 1;
        $validator->value = StringService::stringToFloat($value);
        $validator->num_code = $currency['num_code'];
        $validator->char_code = $currency['char_code'];
        $validator->name = $currency['name'];
        $validator->bank = $bank;

        return $validator;
    }

    /**
     * Валидация курса валют
     * @param SaveExchangeRateValidator $validator
     * @return bool
     */
    private static function validateExchangeRate(SaveExchangeRateValidator $validator): bool
    {
        if ($validator->validate()) {
            return true;
        }
        $errors = $validator->getErrors();
        foreach ($errors as $errorArr) {
            foreach ($errorArr as $error) {
                LogService::error("Ошибка при сохранении курса валют: $error");
            }
        }
        return false;
    }

    /**
     * Сохранить курс валюты
     * @param SaveExchangeRateValidator $rateValidator
     * @return void
     * @throws HttpException
     */
    private static function saveExchangeRate(SaveExchangeRateValidator $rateValidator): void
    {
        if (!$rateValidator->id) {
            $currentRate = new ExchangeRatesModel();
        } else {
            $currentRate = ExchangeRatesModel::findOne(['id' => $rateValidator->id]);
        }
        if (!$currentRate) {
            throw new HttpException(404, "Курс валюты с id $rateValidator->id не найден");
        }
        if ($rateValidator->value > 0) {
            $currentRate->setAttributes($rateValidator->getAttributes(), false);
            $currentRate->save();
        }
    }

    /**
     * Возвращает курс валюты
     * @param int $currencyCode
     * @param int|null $uDate
     * @return float
     * @throws Exception
     */
    public static function get(int $currencyCode, ?int $uDate = null): float
    {
        if ($currencyCode == self::DEFAULT_CURRENCY_NUM_CODE) {
            return 1;
        }
        $date = $uDate ? date('Y-m-d', $uDate) : null;
        /** @var ExchangeRatesModel $rate */
        $rate = ExchangeRatesModel::find()
            ->where(['num_code' => $currencyCode])
            ->andWhere(['>', 'value', 0])
            ->andFilterWhere(['<=', 'date', $date])
            ->orderBy('date desc, bank desc')
            ->limit(1)
            ->one();
        if (!$rate) {
            return 0;
        }
        return $rate->value;
    }

    /**
     * Возвращает все курсы валют
     * @param int|null $uDate
     * @return array
     * @throws Exception
     */
    public static function getAllExchangeRates(?int $uDate = null): array
    {
        $currencies = CurrenciesModel::getOptions();
        $result['date'] = date('d.m.y', $uDate ?? time());
        $result['rates'] = [];
        foreach ($currencies as $currency) {
            $result['rates'][] = [
                'code' => $currency['value'],
                'value' => self::get($currency['key']),
            ];
        }
        return $result;
    }

    /**
     * Возвращает список валют для запроса курсов
     * @return array
     */
    public static function getCurrenciesList(): array
    {
        return CurrenciesModel::find()
            ->select(['*', 'id as num_code'])
            ->where(['!=', 'id', self::DEFAULT_CURRENCY_NUM_CODE])
            ->orderBy('id')
            ->asArray()
            ->all();
    }
}
