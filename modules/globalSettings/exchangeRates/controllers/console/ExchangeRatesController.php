<?php

namespace modules\globalSettings\exchangeRates\controllers\console;

use Exception;
use modules\globalSettings\exchangeRates\services\ExchangeRatesService;
use modules\globalSettings\exchangeRates\validators\ExchangeRatesValidator;
use Yii;
use yii\console\Controller;

class ExchangeRatesController extends Controller
{
    /**
     * Записать в базу текущий курс валют ЦБР
     * @throws Exception
     */
    public function actionCurrentRateSave(): void
    {
        $validator = new ExchangeRatesValidator();
        $date = date('Y-m-d');
        $validator->date_from = $date;
        $validator->date_to = $date;
        $validator->currencies = ExchangeRatesService::getCurrenciesList();
        ExchangeRatesService::saveCBRExchangeRates($validator);
        echo "Finished\n\r";
    }

    /**
     * Записать в базу текущий курс валют MOEX
     * @throws Exception
     */
    public function actionCurrentMoexRateSave(): void
    {
        $validator = new ExchangeRatesValidator();
        $date = date('Y-m-d');
        $validator->date_from = $date;
        $validator->date_to = $date;
        $validator->currencies = ExchangeRatesService::getCurrenciesList();
        ExchangeRatesService::saveMOEXExchangeRates($validator);
        echo "Finished\n\r";
    }

    /**
     * Записать в базу динамику курсов валют с 01.01.2022 по текущий момент
     * @throws Exception
     */
    public function actionOldRateSave(string $date): void
    {
        $validator = new ExchangeRatesValidator();
        $validator->date_from = date('Y-m-d', strtotime($date));
        $validator->date_to = date('Y-m-d');
        $validator->currencies = ExchangeRatesService::getCurrenciesList();
        Yii::$app->db->createCommand('DELETE FROM exchange_rates WHERE 1')->execute();
        ExchangeRatesService::saveCBRExchangeRates($validator);
        echo "All exchange rates saved\n\r";
    }
}
