<?php

namespace modules\globalSettings\exchangeRates\controllers;

use backend\controllers\ApiController;
use backend\validators\common\ValidatorHelpers;
use Exception;
use modules\globalSettings\exchangeRates\services\ExchangeRatesService;
use modules\globalSettings\exchangeRates\validators\ExchangeRatesValidator;
use modules\globalSettings\index\services\GlobalSettingsService;
use yii\web\HttpException;

class IndexApiController extends ApiController
{
    /**
     * Получение таблицы курсов валют
     * @throws HttpException
     * @throws Exception
     */
    public function actionGetList(): array
    {
        GlobalSettingsService::access();
        $validator = ValidatorHelpers::createFromPost(ExchangeRatesValidator::class);
        if (!$validator->validate()) {
            return [];
        }
        /** @var ExchangeRatesValidator $validator */
        return ExchangeRatesService::getExchangeRatesTable($validator);
    }
}
