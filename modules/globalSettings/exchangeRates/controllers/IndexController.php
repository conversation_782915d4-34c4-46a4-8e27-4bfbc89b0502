<?php

namespace modules\globalSettings\exchangeRates\controllers;

use common\models\CurrenciesModel;
use modules\_common\ItrController;
use modules\globalSettings\exchangeRates\services\ExchangeRatesService;
use modules\globalSettings\exchangeRates\validators\ExchangeRatesValidator;
use modules\globalSettings\index\services\GlobalSettingsService;
use Yii;
use yii\web\HttpException;

class IndexController extends ItrController
{
    /**
     * Страница Курса валют
     * @throws HttpException
     */
    public function actionIndex(): string
    {
        GlobalSettingsService::access();
        $this->params['currencies'] = CurrenciesModel::getOptions();
        $this->setMainMenuAccess();
        $this->setFirmParams();
        $this->setRightMenuAccess();
        $this->setPageTitle([['title' => 'Курс валют']]);
        return $this->renderReact([]);
    }

    /**
     * Выгрузка динамики валют excel
     * @throws HttpException
     * @throws \Exception
     */
    public function actionExcel(): string
    {
        GlobalSettingsService::access();
        $this->layout = '/excel';
        header('content-disposition:attachment; filename=rates_' . date('Y_m_d_H_i_s') . '.xls');
        header('content-type:application/vnd.ms-excel; charset=utf-8');
        $validator = new ExchangeRatesValidator();
        $validator->setAttributes(Yii::$app->request->get(), false);
        if (!$validator->validate()) {
            return '';
        }
        Yii::$app->view->params['dynamic'] = ExchangeRatesService::getExchangeRatesTable($validator);
        return $this->render('/ExchangeRatesTable');
    }
}
