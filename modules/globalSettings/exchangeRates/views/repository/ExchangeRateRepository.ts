import { inject, injectable } from 'inversify';
import { typesDi } from '../../../../_common/view/di/types-di';
import { HttpService } from '../../../../_common/view/services';

@injectable()
export class ExchangeRateRepository {
  constructor(@inject(typesDi.HttpService) private httpService: HttpService) {}

  async getList(data: any): Promise<any> {
    return await this.httpService.post('/global-settings/exchange-rates/index-api/get-list/', data);
  }
}
