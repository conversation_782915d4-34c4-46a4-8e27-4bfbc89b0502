import { DivError, MultiCheckbox } from '../../../_common/view/components';
import { ContainerTabPage, TimeFilter } from '../../../_common/view/layout';
import React, { useEffect, useState } from 'react';
import { errorHandler } from '../../../_common/view/handlers/errorHandler';
import { getTitle, toURL } from '../../../_common/view/handlers/functions';
import { baseUrl } from '../../../../conf';
import containerDi from '../../../_common/view/di/container';
import { typesDi } from '../../../_common/view/di/types-di';
import { ExchangeRateRepository } from './repository/ExchangeRateRepository';
import { GlobalSettingsMenu } from '../../index/views/layout/GlobalSettingsMenu';
import style from './styles/ExcahngeRateCard.module.scss';
import cn from 'classnames';
import { multiCheckboxChange } from '../../../_common/view/handlers/changeHandlers';
import { DropButton } from '../../../_common/view/components/form/MultiCheckbox/DropButton';
import { PrintButton } from '../../../_common/view/layout/buttons/PrintButton';
import { ITableHeader, ITableRow } from '../../../_common/view/interfaces';
import { DataGrid } from '../../../_common/view/layout/DataGrid';
import { UpdateButton } from '../../../_common/view/layout/buttons/UpdateButton';
import { ColumnAlignType } from '../../../_common/view/types';

export const ExchangeRatesPage = () => {
  const repository = containerDi.get<ExchangeRateRepository>(typesDi.ExchangeRateRepository);
  const currencies = (document as any).currencies;
  const date = new Date().toISOString().slice(0, 10);
  const [isLoading, setIsLoading] = useState(false);
  const [rows, setRows] = useState([]);
  const [errors, setErrors] = useState({});
  const [model, setModel] = useState({
    date_from: date,
    date_to: date,
    currencies: currencies.map((item) => item.key),
  });
  const access = (document as any).access;

  const header: ITableHeader[] = [
    { value: 'Дата курса', width: 120, name: 'date' },
    { value: 'Цифровой код валюты', width: 150, name: 'num_code' },
    { value: 'Буквенный код валюты', width: 150, name: 'char_code' },
    { value: 'Единиц', width: 100, name: 'units' },
    { value: 'Курс', width: 100, name: 'rate' },
    { value: 'Валюта', width: 120, name: 'currency' },
    { value: 'Банк', width: 100, name: 'bank' },
    { value: '', width: 'auto', name: '' },
  ];

  const prepareExchanges = (rows): Array<ITableRow> => {
    return rows.map((row): ITableRow => {
      return {
        id: row.id,
        columns: [
          { value: row.formatted_date, name: 'date', align: ColumnAlignType.center },
          { value: row.num_code, name: 'num_code', align: ColumnAlignType.right },
          { value: row.char_code, name: 'char_code' },
          { value: row.nominal, name: 'nominal', align: ColumnAlignType.right },
          { value: row.value, name: 'value', align: ColumnAlignType.right },
          { value: row.name, name: 'name' },
          { value: row.bank, name: 'bank' },
          { value: '', name: '' },
        ],
        checkboxSelect: false,
      };
    });
  };

  const printExcel = () => {
    const params = toURL(model);
    window.location.href = baseUrl + '/global-settings/exchange-rates/excel/' + params.toString();
  };

  const onDateChange = ({ start, finish }) => {
    setModel({ ...model, ...{ date_from: start, date_to: finish } });
  };

  const getTable = async () => {
    try {
      await setIsLoading(true);
      let response = await repository.getList(model);
      let preparedRows = await prepareExchanges(response);
      await setRows(preparedRows);
      await setIsLoading(false);
    } catch (e) {
      await setIsLoading(false);
      errorHandler(e, setErrors);
    }
  };

  const currenciesSelect = ({ name, value, checked }) => {
    multiCheckboxChange({ name, value, checked, model, setModel });
  };

  useEffect(() => {
    getTable();
    document.title = getTitle(document.title);
    (window as any).getTable = getTable;
  }, []);

  useEffect(() => {
    getTable();
  }, [model]);

  return (
    <ContainerTabPage>
      <GlobalSettingsMenu />
      <div className="vspan0 container-form">
        <div className="container-wrap">
          <div className="container">
            <ul className="toolbar-filter">
              <li>
                <TimeFilter onDateChange={onDateChange} dStart={model.date_from} dFinish={model.date_to} />
              </li>
              <li className="divider" />
              <li>
                <MultiCheckbox
                  caption="Валюта"
                  name="currencies"
                  values={model.currencies}
                  options={currencies}
                  readonly={false}
                  onChange={currenciesSelect}
                  className={cn(style.selector, style.centred)}
                  DropButton={DropButton}
                />
              </li>
            </ul>
            <ul className={'toolbar'}>
              <li>
                <UpdateButton onClick={() => getTable()} />
              </li>
              <li>
                <PrintButton onClickExcel={printExcel} />
              </li>
            </ul>
            <div className="vspan0">
              <DataGrid
                setRows={setRows}
                windowParams={[]}
                rows={rows}
                header={header}
                footer={{ count: rows.length }}
                isLoading={isLoading}
                resizeable={false}
              />
              <DivError setErrors={setErrors} errors={Object.values(errors)} />
            </div>
          </div>
        </div>
      </div>
    </ContainerTabPage>
  );
};
