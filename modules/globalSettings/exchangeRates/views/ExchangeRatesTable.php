<table>
  <colgroup>
    <col width="120">
    <col width="150">
    <col width="150">
    <col width="100">
    <col width="100">
    <col width="100">
    <col width="100">
  </colgroup>
  <thead>
  <tr>
    <th class="c">Дата курса</th>
    <th class="c">Цифровой код валюты</th>
    <th class="c">Буквенный код валюты</th>
    <th class="c">Единиц</th>
    <th class="c">Курс</th>
    <th class="c">Валюта</th>
    <th class="c">Банк</th>
  </tr>
  </thead>
  <tbody>
  <?php foreach ($this->params['dynamic'] as $item): ?>
    <tr>
      <td class="c"><?php echo $item['date']; ?></td>
      <td class="r"><?php echo $item['num_code']; ?></td>
      <td class=""><?php echo $item['char_code']; ?></td>
      <td class="r"><?php echo $item['nominal']; ?></td>
      <td class="r"><?php echo $item['value']; ?></td>
      <td class=""><?php echo $item['name']; ?></td>
      <td class=""><?php echo $item['bank']; ?></td>
    </tr>
  <?php endforeach; ?>
  <?php if (!count($this->params['dynamic'])): ?>
    <tr class="group">
      <td class="c" colspan="100%">Данные для отображения отсутствуют!</td>
    </tr>
  <?php endif; ?>
  </tbody>
</table>