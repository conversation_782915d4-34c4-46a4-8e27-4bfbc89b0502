<?php
namespace modules\globalSettings\taxes\taxMultipliers\services;
use backend\interfaces\ImportValidatorInterface;
use backend\validators\common\DeleteValidator;
use common\interfaces\ImportServiceInterface;
use common\models\TaxMultipliersModel;
use common\services\traits\ImportServiceTrait;
use Exception;
use modules\globalSettings\taxes\taxMultipliers\validators\GetListValidator;
use modules\globalSettings\taxes\taxMultipliers\validators\SaveValidator;
use Yii;
use yii\db\ActiveQuery;
use yii\db\StaleObjectException;
use yii\web\HttpException;
use yii\web\NotFoundHttpException;

class TaxMultipliersService implements ImportServiceInterface
{
    use ImportServiceTrait;

    protected static int $limit = 100;

    /**
     * Возвращает список повышающих коэффициентов
     * @param  GetListValidator  $validator
     *
     * @return array
     */
    public static function getList(GetListValidator $validator): array
    {
        $offset = $validator->page * self::$limit;
        $multiplierQuery = self::getListQuery($validator);

        $count = $multiplierQuery->count();

        if ($offset >= $count) {
            $offset = 0;
        }

        $multipliers = $multiplierQuery
            ->limit(self::$limit)
            ->offset($offset)
            ->orderBy('id desc')
            ->all();
        return [
            'multipliers' => $multipliers,
            'count' => $count,
            'row_count' => self::$limit,
            'offset' => $offset,
        ];
    }

    /**
     * Возвращает один повышающий коэффициент
     *
     * @param  int|null  $id
     *
     * @return array
     * @throws HttpException
     */
    public static function get(?int $id): array
    {
        if (!$id) {
            return (new TaxMultipliersModel())->getAttributes();
        }
        $multiplier = TaxMultipliersModel::findOne(['id' => $id]);
        if (!$multiplier) {
            throw new HttpException(404, 'Повышающий коэффициент не найден');
        }
        return $multiplier->getAttributes();
    }

    /**
     * Возвращает запрос
     * @param  GetListValidator  $validator
     *
     * @return ActiveQuery
     */
    protected static function getListQuery(GetListValidator $validator): ActiveQuery
    {
        $query = TaxMultipliersModel::find()
            ->where(['tax_year' => $validator->tax_year])
            ->andWhere(['mark' => $validator->mark])
            ->andWhere(['engine_type' => $validator->engine_type])
            ->andWhere(['multiplier' => $validator->multiplier]);

        if ($validator->model) {
            $query->andWhere(['like', 'model', "%$validator->model%", false]);
        }
        return $query;
    }

    /**
     * Сохраняет повышающий коэффициент
     *
     * @param  SaveValidator|ImportValidatorInterface  $validator
     *
     * @return int
     */
    public static function save(SaveValidator|ImportValidatorInterface $validator): int
    {
        if (!$validator->id) {
            $multiplier = new TaxMultipliersModel();
            $multiplier->author = Yii::$app->user->getId();
        } else {
            $multiplier = TaxMultipliersModel::findOne(['id' => $validator->id]);
        }
        $multiplier->setAttributes($validator->attributes, false);
        $multiplier->save();
        return $multiplier->id;
    }

    /**
     * @throws StaleObjectException
     * @throws Throwable
     * @throws NotFoundHttpException
     */
    public static function delete(DeleteValidator $validator)
    {
        $tax = TaxMultipliersModel::findOneSelect(['id' => $validator->id], ['id']);
        if (!$tax) {
            throw new NotFoundHttpException("Коэффициент с id {$validator->id} не найден");
        }
        try {
            $tax->delete();
        } catch (Exception $e) {
            throw new Exception($e);
        }
        return $validator->id;
    }
}
