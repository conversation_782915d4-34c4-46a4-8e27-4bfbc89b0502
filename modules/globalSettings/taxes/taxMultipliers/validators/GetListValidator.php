<?php
namespace modules\globalSettings\taxes\taxMultipliers\validators;

use backend\validators\common\ArrayValidator;
use yii\base\Model;

class GetListValidator extends Model
{
    public $tax_year;
    public $mark;
    public $model;
    public $engine_type;
    public $multiplier;
    public $page;

    public function rules(): array
    {
        return [
            [['page'], 'default', 'value' => 0],
            [['mark', 'tax_year', 'engine_type', 'multiplier'], 'default', 'value' => []],
            [['mark', 'tax_year', 'engine_type', 'multiplier'], ArrayValidator::class],
            [['model'], 'default', 'value' => ''],
            [['model'], 'string'],
        ];
    }
}
