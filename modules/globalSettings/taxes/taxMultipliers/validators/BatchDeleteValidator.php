<?php
namespace modules\globalSettings\taxes\taxMultipliers\validators;

use backend\validators\common\ArrayValidator;
use backend\validators\common\NumberValidator;
use yii\base\Model;

class BatchDeleteValidator extends Model
{
    public $firm;
    public $multipliers;

    public function rules(): array
    {
        return [
            [['firm', 'multipliers'], 'required'],
            [['firm'], NumberValidator::class],
            [['multipliers'], ArrayValidator::class],
        ];
    }
}
