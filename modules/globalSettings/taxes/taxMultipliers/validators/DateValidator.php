<?php

namespace modules\globalSettings\taxes\taxMultipliers\validators;

use yii\validators\Validator;

class DateValidator extends Validator
{
    public function validateAttribute($model, $attribute): void
    {
        $model->date_from = date('Y-m-d', strtotime($model->date_from));
        $model->date_to = date('Y-m-d', strtotime($model->date_to));
        if ($model->date_from > $model->date_to) {
            $this->addError($model, 'date_from', 'Дата от не может быть меньше даты до');
        }
    }
}
