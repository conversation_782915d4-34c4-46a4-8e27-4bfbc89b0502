<?php
namespace modules\globalSettings\taxes\taxMultipliers\validators;

use backend\filters\request\FloatFilter;
use backend\filters\request\QuoteFilter;
use backend\interfaces\ImportValidatorInterface;
use backend\validators\common\NumberValidator;
use yii\base\Model;

class SaveValidator extends Model implements ImportValidatorInterface
{
    public $id;
    public $firm;
    public $tax_year;
    public $price_category;
    public $multiplier;
    public $mark;
    public $model;
    public $engine_type;
    public $engine_volume;
    public $product_year_from;
    public $product_year_up_to;

    public function rules(): array
    {
        return [
            [
                [
                    'tax_year',
                    'price_category',
                    'multiplier',
                    'mark',
                    'model',
                    'engine_type',
                    'engine_volume',
                    'product_year_from',
                    'product_year_up_to',
                ],
                'required',
            ],
            [['multiplier', 'engine_volume'], FloatFilter::class],
            [
                ['id', 'tax_year', 'multiplier', 'engine_volume', 'product_year_from', 'product_year_up_to'],
                NumberValidator::class,
            ],
            [['price_category', 'mark', 'model', 'engine_type'], 'string'],
            [['mark', 'model'], QuoteFilter::class],
            [['id'], 'default', 'value' => null],
        ];
    }

    public function attributeLabels(): array
    {
        return [
            'tax_year' => 'Налоговый год',
            'price_category' => 'Категория ТС',
            'multiplier' => 'Коэффициент',
            'mark' => 'Марка',
            'model' => 'Модель',
            'engine_type' => 'Тип двигателя',
            'engine_volume' => 'Объем двигателя',
            'product_year_from' => 'Год выпуска от',
            'product_year_up_to' => 'Год выпуска до',
        ];
    }
}
