<?php
namespace modules\globalSettings\taxes\taxMultipliers\controllers;

use backend\controllers\ApiController;
use backend\validators\common\DeleteValidator;
use common\services\AutoParkService;
use Exception;
use modules\globalSettings\taxes\taxMultipliers\services\TaxMultipliersService;
use modules\globalSettings\taxes\taxMultipliers\validators\BatchDeleteValidator;
use modules\globalSettings\taxes\taxMultipliers\validators\GetListValidator;
use modules\globalSettings\taxes\taxMultipliers\validators\SaveValidator;
use Throwable;
use Yii;
use yii\db\StaleObjectException;
use yii\web\HttpException;
use yii\web\NotFoundHttpException;

class IndexApiController extends ApiController
{
    public function actionGetList(): array
    {
        $post = Yii::$app->request->post();
        $validator = new GetListValidator();
        $validator->setAttributes($post, false);
        if (!$validator->validate()) {
            return ['errors' => ['message' => $validator->errors]];
        }
        return TaxMultipliersService::getList($validator);
    }

    public function actionSave(): array
    {
        $post = Yii::$app->request->post();
        $validator = new SaveValidator();
        $validator->setAttributes($post, false);
        if (!$validator->validate()) {
            return $this->getReactItrErrors($validator);
        }
        return [
            'id' => TaxMultipliersService::save($validator),
        ];
    }

    /**
     * @throws Throwable
     * @throws HttpException
     */
    public function actionBatchDelete(): array
    {
        $post = Yii::$app->request->post();
        $validator = new BatchDeleteValidator();
        $validator->setAttributes($post, false);
        $validator->firm = AutoParkService::getInstance()->getCurrentPark();
        if (!$validator->validate()) {
            return ['errors' => ['message' => $validator->errors]];
        }
        $dValidator = new DeleteValidator();
        $dValidator->firm = $validator->firm;
        $result = [];
        foreach ($validator->multipliers as $multiplier) {
            $dValidator->id = $multiplier;
            try {
                $result[] = TaxMultipliersService::delete($dValidator);
            } catch (Exception) {
                $result[] = 'Не удалось удалить ' . $multiplier;
            }
        }
        return [
            'status' => 200,
            'taxes' => $result,
        ];
    }

    /**
     * @return array
     * @throws HttpException
     * @throws Throwable
     * @throws StaleObjectException
     * @throws NotFoundHttpException
     */
    public function actionDelete(): array
    {
        $post = Yii::$app->request->post();
        $validator = new DeleteValidator();
        $validator->setAttributes($post, false);
        $validator->firm = AutoParkService::getInstance()->getCurrentPark();
        if (!$validator->validate()) {
            return $this->getReactItrErrors($validator);
        }
        return [
            'id' => TaxMultipliersService::delete($validator),
        ];
    }
}
