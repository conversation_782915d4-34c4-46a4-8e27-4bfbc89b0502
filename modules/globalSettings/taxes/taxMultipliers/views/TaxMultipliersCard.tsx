import { MenuHorizontal } from '../../../../_common/view/menu';
import { ContainerForm, ContainerPage } from '../../../../_common/view/layout';
import {
  AsNumberInput,
  CloseButton,
  DeleteButton,
  DivError,
  FormGroup,
  Input,
  SaveButton,
} from '../../../../_common/view/components';
import { IMenuItem } from '../../../../_common/view/interfaces';
import { IconConst } from '../../../../_common/view/const';
import { useState } from 'react';
import containerDi from '../../../../_common/view/di/container';
import { typesDi } from '../../../../_common/view/di/types-di';
import { errorHandler } from '../../../../_common/view/handlers/errorHandler';
import { keyboardControl } from '../../../../_common/view/handlers/keyboardControl';
import { ConfigService } from '../../../../_common/view/services';
import { TaxMultipliersRepository } from './repository/TaxMultipliersRepository';
import { getTitle } from '../../../../_common/view/handlers/functions';

let defaultModel: object = {};

const repository = containerDi.get<TaxMultipliersRepository>(typesDi.TaxMultipliersRepository);
const configService = containerDi.get<ConfigService>(typesDi.ConfigService);
const legacyUrl = configService.legacyUrl();

export const TaxMultipliersCard = () => {
  const [model, setModel] = useState((document as any).data);
  const [id, setId] = useState(Number(new URLSearchParams(window.location.search).get('id')));
  const [readonly, setReadonly] = useState(id > 0);
  const [userErrors, setErrors] = useState({});
  const [isLoading, setIsLoading] = useState(false);
  const access: boolean = (document as any).access;
  const isAdmin: boolean = (document as any).is_admin;
  document.title = getTitle((document as any).title);

  if (Object.keys(defaultModel).length == 0) {
    defaultModel = { ...model };
  }

  const menuItems: Array<IMenuItem> = [
    {
      icon: IconConst.chartUp,
      title: 'Коэффициент',
      href: `/global-settings/taxes/tax-multipliers/`,
      id: 'tax-multipliers',
      params: { id: id },
    },
  ];
  if (id) {
    menuItems.push({
      icon: IconConst.changes,
      title: 'Правки',
      href: legacyUrl + `/car/tax-multiplier/access?id=${id}`,
      id: 'access',
    });
  }

  const save = async (e) => {
    e.preventDefault();
    setIsLoading(true);
    try {
      await setModel((prevState) => ({
        ...prevState,
        ['model']: quoteFilter(model.model),
        ['mark']: quoteFilter(model.mark),
      }));

      await repository.saveCard(model).then((data) => {
        if (!id) {
          window.history.pushState('', '', `?id=${data.id}`);
          setId(data.id);
          setModel((prevState) => ({ ...prevState, id: data.id }));
        }
        setErrors({});
        defaultModel = { ...model };
        setReadonly(true);
        window.opener.postMessage('update_table', '*');
        setIsLoading(false);
      });
    } catch (e) {
      errorHandler(e, setErrors);
      setIsLoading(false);
    }
  };

  const quoteFilter = (str) => {
    return str ? str.replace(/["'«»“„]/gi, '') : '';
  };

  const deleteCard = async () => {
    if (confirm('Удалить коэффициент?')) {
      setIsLoading(true);
      try {
        await repository.deleteCard(id);
        window.opener.postMessage('update_table', '*');
        window.close();
      } catch (e) {
        errorHandler(e, setErrors);
        setIsLoading(false);
      }
    }
  };

  const handleChange = ({ name, value }) => {
    setModel((prevState) => ({ ...prevState, [name]: value }));
  };

  document.onkeyup = (e) => keyboardControl({ setModel, defaultModel, setReadonly, readonly, save, e });

  return (
    <div>
      <ContainerPage>
        <MenuHorizontal items={menuItems} />
        <ContainerForm>
          <div className="vspan0 scroll-y scroll-padding">
            <div className="form-horizontal label-large">
              <FormGroup label="Налоговый год:">
                <AsNumberInput
                  readonly={readonly}
                  name="tax_year"
                  value={model.tax_year}
                  onChange={handleChange}
                  useGrouping={false}
                  minimumFractionDigits={0}
                  maxLength={4}
                />
              </FormGroup>
              <FormGroup label="Категория ТС:">
                <Input readonly={readonly} name="price_category" value={model.price_category} onChange={handleChange} />
              </FormGroup>
              <FormGroup label="Коэффициент:">
                <AsNumberInput readonly={readonly} name="multiplier" value={model.multiplier} onChange={handleChange} />
              </FormGroup>
              <FormGroup label="Марка:">
                <Input readonly={readonly} name="mark" value={model.mark} onChange={handleChange} />
              </FormGroup>
              <FormGroup label="Модель:">
                <Input readonly={readonly} name="model" value={model.model} onChange={handleChange} />
              </FormGroup>
              <FormGroup label="Тип двигателя:">
                <Input readonly={readonly} name="engine_type" value={model.engine_type} onChange={handleChange} />
              </FormGroup>
              <FormGroup label="Объем двигателя:">
                <AsNumberInput
                  readonly={readonly}
                  name="engine_volume"
                  value={model.engine_volume}
                  onChange={handleChange}
                  minimumFractionDigits={0}
                />
              </FormGroup>
              <FormGroup label="Год выпуска ТС от:">
                <AsNumberInput
                  readonly={readonly}
                  name="product_year_from"
                  value={model.product_year_from}
                  onChange={handleChange}
                  useGrouping={false}
                  minimumFractionDigits={0}
                  maxLength={4}
                />
              </FormGroup>
              <FormGroup label="Год выпуска ТС до:">
                <AsNumberInput
                  readonly={readonly}
                  name="product_year_up_to"
                  value={model.product_year_up_to}
                  onChange={handleChange}
                  useGrouping={false}
                  minimumFractionDigits={0}
                  maxLength={4}
                />
              </FormGroup>
            </div>
          </div>
          <div className="buttons">
            <div className="pull-left">
              <DeleteButton access={isAdmin} readonly={readonly} onClick={deleteCard} />
            </div>
            <CloseButton
              readonly={readonly}
              setReadonly={setReadonly}
              setDefaultModel={setModel}
              isLoading={isLoading}
              defaultModel={defaultModel}
            />
            <SaveButton
              access={access}
              readonly={readonly && id > 0}
              setReadonly={setReadonly}
              onClick={save}
              isLoading={isLoading}
            />
          </div>
        </ContainerForm>
      </ContainerPage>
      <DivError errors={Object.values(userErrors)} setErrors={setErrors} />
    </div>
  );
};
