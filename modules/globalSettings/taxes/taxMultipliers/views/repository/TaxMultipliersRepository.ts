import { inject, injectable } from 'inversify';
import { typesDi } from '../../../../../_common/view/di/types-di';
import { HttpService } from '../../../../../_common/view/services/http-service';

@injectable()
export class TaxMultipliersRepository {
  constructor(@inject(typesDi.HttpService) private httpService: HttpService) {}

  async saveCard(data: any): Promise<any> {
    return await this.httpService.post('/global-settings/taxes/tax-multipliers/index-api/save/', data);
  }

  async deleteCard(id: any) {
    return await this.httpService.post('/global-settings/taxes/tax-multipliers/index-api/delete/', { id: id });
  }
}
