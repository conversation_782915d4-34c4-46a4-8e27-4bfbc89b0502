<?php
namespace modules\globalSettings\taxes\taxBenefits\validators;

use backend\filters\request\FloatFilter;
use backend\validators\common\NumberValidator;
use yii\base\Model;

class SaveValidator extends Model
{
    public $id;
    public $region;
    public $year;
    public $engine_type;
    public $multiplier;

    public function rules(): array
    {
        return [
            [['region', 'year', 'engine_type', 'multiplier'], 'required'],
            [['region', 'year', 'engine_type', 'id'], NumberValidator::class],
            [['multiplier'], FloatFilter::class],
            [['multiplier'], NumberValidator::class, 'min' => 0, 'max' => 1],
            [['id'], 'default', 'value' => 0],
        ];
    }
}
