<?php

namespace modules\globalSettings\taxes\taxBenefits\services;

use backend\validators\common\DeleteValidator;
use common\models\TaxBenefitsModel;
use Exception;
use modules\globalSettings\taxes\taxBenefits\validators\SaveValidator;
use Throwable;
use yii\db\StaleObjectException;
use yii\web\HttpException;
use yii\web\NotFoundHttpException;

class TaxBenefitsService
{
    /**
     * Возвращает список льгот
     * @return array
     */
    public static function getList(): array
    {
        $benefits = TaxBenefitsModel::find()
            ->orderBy('id desc')
            ->all();
        $result = [];
        foreach ($benefits as $benefit) {
            $result[] = [
                'id' => $benefit->id,
                'date_create' => $benefit->getDateCreate(),
                'region' => $benefit->getRegion(),
                'engine_type' => $benefit->getEngineType(),
                'year' => $benefit->year,
                'multiplier' => $benefit->multiplier,
            ];
        }
        return [
            'benefits' => $result,
            'count' => TaxBenefitsModel::find()->count(),
        ];
    }

    /**
     * Возвращает льготу
     * @throws HttpException
     */
    public static function get(?int $id): array
    {
        if (!$id) {
            return (new TaxBenefitsModel())->getAttributes();
        }
        $benefit = TaxBenefitsModel::findOne(['id' => $id]);
        if (!$benefit) {
            throw new HttpException(404, 'Льгота не найдена');
        }
        return $benefit->getAttributes();
    }

    /**
     * Сохранение льготы
     * @throws NotFoundHttpException
     */
    public static function save(SaveValidator $validator): int
    {
        $issetBenefit = TaxBenefitsModel::find()
            ->where([
                'region' => $validator->region,
                'year' => $validator->year,
                'engine_type' => $validator->engine_type,
            ])
            ->andWhere(['!=', 'id', $validator->id])
            ->all();
        if ($issetBenefit) {
            throw new NotFoundHttpException('Такая льгота уже есть');
        }
        if ($validator->id) {
            $benefit = TaxBenefitsModel::findOne(['id' => $validator->id]);
        } else {
            $benefit = new TaxBenefitsModel();
        }
        $benefit->setAttributes($validator->attributes, false);
        $benefit->save();
        return $benefit->id;
    }

    /**
     * Удаление льготы
     * @throws NotFoundHttpException
     * @throws Exception
     * @throws Throwable
     */
    public static function delete(DeleteValidator $validator)
    {
        if (!($benefit = TaxBenefitsModel::findOne(['id' => $validator->id]))) {
            throw new NotFoundHttpException('Льгота не найдена');
        }
        try {
            $benefit->delete();
        } catch (StaleObjectException $e) {
            throw new Exception($e);
        }

        return $validator->id;
    }
}
