import { inject, injectable } from 'inversify';
import { typesDi } from '../../../../../_common/view/di/types-di';
import { HttpService } from '../../../../../_common/view/services/http-service';

@injectable()
export class TaxBenefitsRepository {
  constructor(@inject(typesDi.HttpService) private httpService: HttpService) {}

  async saveCard(data: any) {
    return await this.httpService.post('/global-settings/taxes/tax-benefits/index-api/save/', data);
  }

  async deleteCard(id: any) {
    return await this.httpService.post('/global-settings/taxes/index-api/delete/', { id: id });
  }
}
