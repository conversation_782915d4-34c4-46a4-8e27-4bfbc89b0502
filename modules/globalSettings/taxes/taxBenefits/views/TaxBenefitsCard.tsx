import { MenuHorizontal } from '../../../../_common/view/menu';
import { ContainerForm, ContainerPage } from '../../../../_common/view/layout';
import {
  AsNumberInput,
  CloseButton,
  DeleteButton,
  DivError,
  FormGroup,
  SaveButton,
  Select,
} from '../../../../_common/view/components';
import { IMenuItem, IOption } from '../../../../_common/view/interfaces';
import { IconConst } from '../../../../_common/view/const';
import { useState } from 'react';
import containerDi from '../../../../_common/view/di/container';
import { typesDi } from '../../../../_common/view/di/types-di';
import { errorHandler } from '../../../../_common/view/handlers/errorHandler';
import { keyboardControl } from '../../../../_common/view/handlers/keyboardControl';
import { ConfigService } from '../../../../_common/view/services';
import { TaxBenefitsRepository } from './repository/TaxBenefitsRepository';
import { getTitle } from '../../../../_common/view/handlers/functions';

let defaultModel: object = {};

const repository = containerDi.get<TaxBenefitsRepository>(typesDi.TaxBenefitsRepository);
const configService = containerDi.get<ConfigService>(typesDi.ConfigService);
const legacyUrl = configService.legacyUrl();

export const TaxBenefitsCard = () => {
  const [model, setModel] = useState((document as any).data);
  const [readonly, setReadonly] = useState(true);
  const [userErrors, setErrors] = useState({});
  const [id, setId] = useState(Number(new URLSearchParams(window.location.search).get('id')));
  const [isLoading, setIsLoading] = useState(false);
  const regions: Array<IOption> = (document as any).regions;
  const engine_types: Array<IOption> = (document as any).engine_types;
  const access: boolean = (document as any).access;
  const isAdmin: boolean = (document as any).is_admin;
  document.title = getTitle((document as any).title);

  if (Object.keys(defaultModel).length == 0) {
    defaultModel = { ...model };
  }

  const menuItems: Array<IMenuItem> = [
    {
      icon: IconConst.coins,
      title: 'Налоговая льгота',
      href: `/global-settings/taxes/tax-benefits/`,
      id: 'benefits',
      params: { id: id },
    },
  ];
  if (id) {
    menuItems.push({
      icon: IconConst.changes,
      title: 'Правки',
      href: legacyUrl + `/car/tax-benefits/access?id=${id}`,
      id: 'access',
    });
  }

  const handleChange = ({ name, value }) => {
    setModel((prevState) => ({ ...prevState, [name]: value }));
  };

  const save = async (e) => {
    e.preventDefault();
    setIsLoading(true);
    try {
      await repository.saveCard(model).then((data) => {
        if (!id) {
          window.history.pushState('', '', `?id=${data.id}`);
          setId(data.id);
          setModel((prevState) => ({ ...prevState, id: data.id }));
        }
        setErrors({});
        defaultModel = { ...model };
        setReadonly(true);
        window.opener.postMessage('update_table', '*');
        setIsLoading(false);
      });
    } catch (e) {
      errorHandler(e, setErrors);
      setIsLoading(false);
    }
  };

  const deleteCard = async () => {
    if (confirm('Удалить налоговую ставку?')) {
      setIsLoading(true);
      try {
        await repository.deleteCard(id);
        window.opener.postMessage('update_table', '*');
        window.close();
      } catch (e) {
        errorHandler(e, setErrors);
        setIsLoading(false);
      }
    }
  };

  document.onkeyup = (e) => keyboardControl({ setModel, defaultModel, setReadonly, readonly, save, e });

  return (
    <div>
      <ContainerPage>
        <MenuHorizontal items={menuItems} />
        <ContainerForm>
          <div className="vspan0 scroll-y scroll-padding">
            <div className="form-horizontal label-large">
              <FormGroup label="Регион">
                <Select
                  name="region"
                  value={model.region}
                  options={regions}
                  onChange={handleChange}
                  disabled={readonly && id > 0}
                />
              </FormGroup>
              <FormGroup label="Налоговый год">
                <AsNumberInput
                  name="year"
                  value={model.year}
                  useGrouping={false}
                  minimumFractionDigits={0}
                  maxLength={4}
                  onChange={handleChange}
                  readonly={readonly && id > 0}
                />
              </FormGroup>
              <FormGroup label="Тип двигателя">
                <Select
                  name="engine_type"
                  value={model.engine_type}
                  options={engine_types}
                  onChange={handleChange}
                  disabled={readonly && id > 0}
                />
              </FormGroup>
              <FormGroup label="Коэффициент">
                <AsNumberInput
                  name="multiplier"
                  value={model.multiplier}
                  onChange={handleChange}
                  readonly={readonly && id > 0}
                />
              </FormGroup>
            </div>
          </div>
          <div className="buttons">
            <div className="pull-left">
              <DeleteButton access={isAdmin} readonly={readonly && id} onClick={deleteCard} />
            </div>
            <CloseButton
              readonly={readonly}
              setReadonly={setReadonly}
              setDefaultModel={setModel}
              defaultModel={defaultModel}
              isLoading={isLoading}
            />
            <SaveButton
              access={access}
              readonly={readonly && id > 0}
              setReadonly={setReadonly}
              onClick={save}
              isLoading={isLoading}
            />
          </div>
        </ContainerForm>
      </ContainerPage>
      <DivError errors={Object.values(userErrors)} setErrors={setErrors} />
    </div>
  );
};
