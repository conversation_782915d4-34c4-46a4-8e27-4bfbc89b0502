<?php

namespace modules\globalSettings\taxes\taxBenefits\controllers;

use backend\controllers\ApiController;
use backend\validators\common\DeleteValidator;
use common\services\AutoParkService;
use modules\globalSettings\taxes\taxBenefits\services\TaxBenefitsService;
use modules\globalSettings\taxes\taxBenefits\validators\SaveValidator;
use Throwable;
use Yii;
use yii\web\HttpException;
use yii\web\NotFoundHttpException;

class IndexApiController extends ApiController
{
    /**
     * Возвращает список льгот
     * @return array
     */
    public function actionGetList(): array
    {
        return TaxBenefitsService::getList();
    }

    /**
     * Сохранение льготы
     * @throws NotFoundHttpException
     */
    public function actionSave(): array
    {
        $post = Yii::$app->request->post();
        $validator = new SaveValidator();
        $validator->setAttributes($post, false);
        if (!$validator->validate()) {
            return $this->getReactItrErrors($validator);
        }
        return [
            'id' => TaxBenefitsService::save($validator),
        ];
    }

    /**
     * Удаление льготы
     * @throws HttpException
     * @throws Throwable
     */
    public function actionDelete(): array
    {
        $post = Yii::$app->request->post();
        $validator = new DeleteValidator();
        $validator->setAttributes($post, false);
        $validator->firm = AutoParkService::getInstance()->getCurrentPark();
        if (!$validator->validate()) {
            return $this->getReactItrErrors($validator);
        }
        return [
            'id' => TaxBenefitsService::delete($validator),
        ];
    }
}
