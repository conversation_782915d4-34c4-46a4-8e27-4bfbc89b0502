<?php
namespace modules\globalSettings\taxes\taxBenefits\controllers;

use common\constants\EngineTypes;
use common\models\RegionsModel;
use common\services\AccessService;
use Exception;
use modules\_common\ItrController;
use modules\globalSettings\index\services\GlobalSettingsService;
use modules\globalSettings\taxes\taxBenefits\services\TaxBenefitsService;
use Yii;
use yii\web\HttpException;

class IndexController extends ItrController
{
    /**
     * Рендер React-приложения
     *
     * @return string
     * @throws HttpException
     * @throws Exception
     */
    public function actionIndex(): string
    {
        GlobalSettingsService::access();
        $this->error404Handler = 'error404Card';
        $id = Yii::$app->request->get('id', 0);
        $this->setFirmParams();
        $this->params['regions'] = RegionsModel::getOptions();
        $this->params['engine_types'] = EngineTypes::getOptions();
        $this->params['access'] = AccessService::menu('global');
        $this->setPageTitle([['title' => 'Налоговые льготы', 'id' => $id]]);
        return $this->renderReact(TaxBenefitsService::get($id));
    }
}
