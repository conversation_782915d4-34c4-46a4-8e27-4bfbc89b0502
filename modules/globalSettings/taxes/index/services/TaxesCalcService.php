<?php

namespace modules\globalSettings\taxes\index\services;

use common\constants\EngineTypes;
use common\models\CarsModel;
use common\models\CarTaxesModel;
use common\models\FilialsModel;
use common\models\HelpersModel;
use common\models\TaxBenefitsModel;
use common\models\TaxMultipliersModel;

class TaxesCalcService
{
    private CarsModel $car;
    private FilialsModel $filial;

    public function __construct(CarsModel $car, FilialsModel $filial)
    {
        $this->car = $car;
        $this->filial = $filial;
    }

    /**
     * Расчет транспортного налога
     *
     * @param int $taxYear Налоговый год
     * @param float $holdingPeriod Период владения
     * @param float $adjustment Корректировка
     *
     * @return float
     */
    public function calc(int $taxYear, float $holdingPeriod, float $adjustment): float
    {
        $taxRate = $this->getRate($taxYear);
        $taxMultiplier = $this->getMitMultiplier($taxYear);
        $benefit = $this->getBenefit($taxYear);

        return $taxRate * $this->car->engine_power * $holdingPeriod * $taxMultiplier * $adjustment * $benefit;
    }

    /**
     * Коэффициенты транспортного налога
     *
     * @param int $taxYear Налоговый год
     * @param float $holdingPeriod Период владения
     * @param float $adjustment Корректировка
     *
     * @return string
     */
    public function getCoefficients(int $taxYear, float $holdingPeriod, float $adjustment): string
    {
        $taxRate = $this->getRate($taxYear);
        $taxMultiplier = $this->getMitMultiplier($taxYear);
        $benefit = $this->getBenefit($taxYear);

        return "НС х МОЩ х ПЕР х ПОВ.КОЭФ х КОРР х СКИДКА \n$taxRate х {$this->car->engine_power} х $holdingPeriod х $taxMultiplier х $adjustment х $benefit";
    }

    /**
     * Возвращает налоговую ставку
     *
     * @param int $taxYear Налоговый год
     * @return float
     */
    public function getRate(int $taxYear): float
    {
        $carAge = $taxYear - floor($this->car->year) + 1;
        $carTax = CarTaxesModel::find()
            ->select(['rate'])
            ->where([
                'trans_category' => $this->car->trans_category,
                'region' => $this->filial->tax_region,
                'year' => $taxYear,
            ])
            ->andWhere(['<', 'min_engine_power', $this->car->engine_power])
            ->andWhere(['>=', 'max_engine_power', $this->car->engine_power])
            ->andWhere(['<', 'car_age_from', $carAge])
            ->andWhere(['>=', 'car_age_up_to', $carAge])
            ->one();
        return $carTax?->rate ?? 0;
    }

    /**
     * Возвращает повышающий коэффициент из Минпромторга
     * @param int $taxYear Налоговый год
     * @return float
     */
    public function getMitMultiplier(int $taxYear): float
    {
        $mark = HelpersModel::get($this->car->mark);
        $model = HelpersModel::get($this->car->model);
        $multiplier = TaxMultipliersModel::find()
            ->select(['multiplier'])
            ->where([
                'mark' => $mark,
                'model' => $model,
                'tax_year' => $taxYear,
                'engine_volume' => $this->car->engine_volume,
            ])
            ->andWhere(['<=', 'product_year_from', $this->car->year])
            ->andWhere(['>=', 'product_year_up_to', $this->car->year])
            ->andWhere(['like', 'engine_type', EngineTypes::get($this->car->engine_type), false])
            ->one();
        return $multiplier?->multiplier ?? 1.0;
    }

    /**
     * Возвращает налоговую льготу
     * @param int $taxYear Налоговый год
     *
     * @return float
     */
    public function getBenefit(int $taxYear): float
    {
        $benefit = TaxBenefitsModel::find()
            ->select(['multiplier'])
            ->where([
                'region' => $this->filial->tax_region,
                'engine_type' => $this->car->engine_type,
                'year' => $taxYear,
            ])
            ->one();
        return $benefit?->multiplier ?? 1.0;
    }
}
