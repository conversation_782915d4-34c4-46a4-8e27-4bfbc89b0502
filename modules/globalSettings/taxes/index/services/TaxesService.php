<?php
namespace modules\globalSettings\taxes\index\services;

use backend\interfaces\ImportValidatorInterface;
use backend\validators\common\DeleteValidator;
use common\constants\TransportCategories;
use common\interfaces\ImportServiceInterface;
use common\models\CarTaxesModel;
use common\models\RegionsModel;
use common\services\traits\ImportServiceTrait;
use Exception;
use modules\globalSettings\taxes\index\validators\GetListValidator;
use modules\globalSettings\taxes\index\validators\SaveValidator;
use Yii;
use yii\db\ActiveQuery;
use yii\web\NotFoundHttpException;

class TaxesService implements ImportServiceInterface
{
    use ImportServiceTrait;

    private static int $limit = 100;

    /**
     * Возвращает список налоговых ставок
     * @param  GetListValidator  $validator
     *
     * @return array
     */
    public static function getList(GetListValidator $validator): array
    {
        $offset = self::$limit * $validator->page;
        $taxesQuery = self::getListQuery($validator);
        $count = $taxesQuery->count();

        if ($offset >= $count) {
            $offset = 0;
        }

        $taxes = $taxesQuery
            ->offset($offset)
            ->limit(self::$limit)
            ->orderBy('id desc')
            ->all();
        $result = [];
        foreach ($taxes as $tax) {
            $result[] = [
                'id' => $tax->id,
                'year' => $tax->year,
                'region' => $tax->getRegion(),
                'trans_category' => $tax->getTransCategory(),
                'object' => $tax->object,
                'min_engine_power' => $tax->min_engine_power,
                'max_engine_power' => $tax->max_engine_power,
                'car_age_from' => $tax->car_age_from,
                'car_age_up_to' => $tax->car_age_up_to,
                'rate' => $tax->rate,
            ];
        }
        return [
            'taxes' => $result,
            'count' => $taxesQuery->count(),
            'row_count' => self::$limit,
            'offset' => $offset,
        ];
    }

    /**
     * Возвращает запрос списка налоговых ставок
     * @param  GetListValidator  $validator
     *
     * @return ActiveQuery
     */
    private static function getListQuery(GetListValidator $validator): ActiveQuery
    {
        return CarTaxesModel::find()
            ->where(['year' => $validator->year])
            ->andWhere(['region' => $validator->region])
            ->andWhere(['trans_category' => $validator->trans_category]);
    }

    /**
     * Получение одной налоговой ставки
     *
     * @throws NotFoundHttpException
     * @throws Exception
     */
    public static function get(?int $id): array
    {
        if (!$id) {
            return (new CarTaxesModel())->getAttributes();
        }
        $carTax = CarTaxesModel::findOne(['id' => $id]);
        if (!$carTax) {
            throw new NotFoundHttpException('Ставка не найдена');
        }
        $result = $carTax->attributes;
        $result['region_name'] = RegionsModel::get($carTax->region);
        $result['trans_category_name'] = TransportCategories::get($carTax->trans_category);
        return $result;
    }

    /**
     * Сохранение налоговой ставки
     * @param  SaveValidator|ImportValidatorInterface  $validator
     *
     * @return int
     */
    public static function save(SaveValidator|ImportValidatorInterface $validator): int
    {
        if (!$validator->id) {
            $tax = new CarTaxesModel();
            $tax->setAttributes($validator->attributes, false);
            $tax->id = null;
            $tax->author = Yii::$app->user->getId();
        } else {
            $tax = CarTaxesModel::findOne(['id' => $validator->id]);
            $tax->setAttributes($validator->attributes, false);
            $tax->region = $validator->region;
            $tax->year = $validator->year;
            $tax->trans_category = $validator->trans_category;
            $tax->object = $validator->object;
            $tax->min_engine_power = $validator->min_engine_power;
            $tax->max_engine_power = $validator->max_engine_power;
            $tax->car_age_from = $validator->car_age_from;
            $tax->car_age_up_to = $validator->car_age_up_to;
        }
        $tax->save();
        return $tax->id;
    }

    /**
     * Удаление налоговой ставки
     * @throws NotFoundHttpException
     * @throws Exception|\Throwable
     */
    public static function delete(DeleteValidator $validator): int
    {
        $tax = CarTaxesModel::findOneSelect(['id' => $validator->id], ['id']);
        if (!$tax) {
            throw new NotFoundHttpException("Ставка с id {$validator->id} не найдена");
        }
        try {
            $tax->delete();
        } catch (Exception $e) {
            throw new Exception($e);
        }
        return $validator->id;
    }
}
