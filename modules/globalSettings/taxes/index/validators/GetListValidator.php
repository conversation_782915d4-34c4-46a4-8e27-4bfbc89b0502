<?php
namespace modules\globalSettings\taxes\index\validators;

use backend\validators\common\NumberValidator;
use yii\base\Model;

class GetListValidator extends Model
{
    public $page;
    public $year;
    public $region;
    public $trans_category;

    public function rules(): array
    {
        return [
            [['year', 'region', 'trans_category'], 'default', 'value' => []],
            [['page'], 'default', 'value' => 0],
            [['page'], NumberValidator::class],
        ];
    }
}
