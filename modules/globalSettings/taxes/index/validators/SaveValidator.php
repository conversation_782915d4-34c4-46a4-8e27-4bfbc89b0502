<?php
namespace modules\globalSettings\taxes\index\validators;

use backend\filters\request\FloatFilter;
use backend\interfaces\ImportValidatorInterface;
use backend\validators\common\NumberValidator;
use yii\base\Model;

class SaveValidator extends Model implements ImportValidatorInterface
{
    public $firm;
    public $id;
    public $region;
    public $year;
    public $trans_category;
    public $object;
    public $min_engine_power;
    public $max_engine_power;
    public $car_age_from;
    public $car_age_up_to;
    public $rate;

    public function rules(): array
    {
        return [
            [['region', 'year', 'trans_category', 'object', 'rate', 'max_engine_power', 'car_age_up_to'], 'required'],
            [
                ['min_engine_power', 'car_age_from'],
                'required',
                'isEmpty' => function ($value) {
                    return $value == '';
                },
            ],
            [['id'], 'default', 'value' => null],
            [
                ['year', 'car_age_from', 'car_age_up_to', 'min_engine_power', 'max_engine_power', 'rate'],
                FloatFilter::class,
            ],
            [
                [
                    'region',
                    'year',
                    'trans_category',
                    'car_age_from',
                    'car_age_up_to',
                    'min_engine_power',
                    'max_engine_power',
                    'rate',
                ],
                NumberValidator::class,
            ],
            [['object'], 'string'],
        ];
    }

    public function attributeLabels(): array
    {
        return [
            'region' => 'Регион',
            'year' => 'Налоговый год',
            'trans_category' => 'Категория ТС',
            'object' => 'Объект налогооблажения',
            'min_engine_power' => 'Минимальное значение мощности',
            'max_engine_power' => 'Максимальное значение мощности',
            'car_age_from' => 'Возраст ТС от',
            'car_age_up_to' => 'Возраст ТС до',
            'rate' => 'Налоговая ставка',
        ];
    }
}
