<?php
namespace modules\globalSettings\taxes\index\validators;

use backend\validators\common\ArrayValidator;
use backend\validators\common\NumberValidator;
use yii\base\Model;

class BatchDeleteValidator extends Model
{
    public $firm;
    public $taxes;

    public function rules(): array
    {
        return [
            [['firm', 'taxes'], 'required'],
            [['firm'], NumberValidator::class],
            [['taxes'], ArrayValidator::class],
        ];
    }
}
