<?php

namespace modules\globalSettings\taxes\index\controllers;

use common\constants\TransportCategories;
use common\models\RegionsModel;
use common\services\AccessService;
use Exception;
use modules\_common\ItrController;
use modules\globalSettings\index\services\GlobalSettingsService;
use modules\globalSettings\taxes\index\services\TaxesService;
use Yii;
use yii\web\HttpException;

class IndexController extends ItrController
{
    /**
     * Карточка налоговой ставки
     *
     * @return string
     * @throws HttpException
     * @throws Exception
     */
    public function actionIndex(): string
    {
        GlobalSettingsService::access();
        $this->error404Handler = 'error404Card';
        $id = Yii::$app->request->get('id', 0);
        $this->setFirmParams();
        $this->params['regions'] = RegionsModel::getOptions();
        $this->params['trans_categories'] = TransportCategories::getOptions();
        $this->params['access'] = AccessService::menu('global');
        $this->setPageTitle([['title' => 'Налоговая ставка', 'id' => $id]]);
        return $this->renderReact(TaxesService::get($id));
    }
}
