<?php

namespace modules\globalSettings\taxes\index\controllers;

use backend\controllers\ApiController;
use backend\validators\common\DeleteValidator;
use common\services\AutoParkService;
use Exception;
use modules\globalSettings\taxes\index\services\TaxesService;
use modules\globalSettings\taxes\index\validators\BatchDeleteValidator;
use modules\globalSettings\taxes\index\validators\GetListValidator;
use modules\globalSettings\taxes\index\validators\SaveValidator;
use Yii;
use yii\web\HttpException;

class IndexApiController extends ApiController
{
    /**
     * Получение списка налоговых ставок
     * @return array[]
     */
    public function actionGetList(): array
    {
        $post = Yii::$app->request->post();
        $validator = new GetListValidator();
        $validator->setAttributes($post, false);
        if (!$validator->validate()) {
            return ['errors' => ['message' => $validator->errors]];
        }
        return TaxesService::getList($validator);
    }

    /**
     * Сохранение налоговой ставки
     */
    public function actionSave(): array
    {
        $post = Yii::$app->request->post();
        $validator = new SaveValidator();
        $validator->setAttributes($post, false);
        if (!$validator->validate()) {
            return $this->getReactItrErrors($validator);
        }
        return [
            'id' => TaxesService::save($validator),
        ];
    }

    /**
     * Пакетное удаление налоговых ставок
     * @return array
     * @throws \Throwable
     */
    public function actionBatchDelete(): array
    {
        $post = Yii::$app->request->post();
        $validator = new BatchDeleteValidator();
        $validator->setAttributes($post, false);
        if (!$validator->validate()) {
            return ['errors' => ['message' => $validator->errors]];
        }
        $dValidator = new DeleteValidator();
        $dValidator->firm = $validator->firm;
        $result = [];
        foreach ($validator->taxes as $tax) {
            $dValidator->id = $tax;
            try {
                $result[] = TaxesService::delete($dValidator);
            } catch (Exception) {
                $result[] = 'Не удалось удалить ' . $tax;
            }
        }
        return [
            'status' => 200,
            'taxes' => $result,
        ];
    }

    /**
     * Удаление одной налоговой ставки
     * @throws HttpException|\Throwable
     */
    public function actionDelete(): array
    {
        $post = Yii::$app->request->post();
        $validator = new DeleteValidator();
        $validator->setAttributes($post, false);
        $validator->firm = AutoParkService::getInstance()->getCurrentPark();
        if (!$validator->validate()) {
            return $this->getReactItrErrors($validator);
        }
        AutoParkService::getInstance()->setCurrentPark($validator->firm);
        return [
            'id' => TaxesService::delete($validator),
        ];
    }
}
