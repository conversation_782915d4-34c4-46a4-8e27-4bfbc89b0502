import { useState } from 'react';
import { IconConst } from '../../../../_common/view/const';
import { IMenuItem } from '../../../../_common/view/interfaces';
import { ContainerForm, ContainerPage } from '../../../../_common/view/layout';
import { MenuHorizontal } from '../../../../_common/view/menu';
import {
  AsNumberInput,
  CloseButton,
  DeleteButton,
  DivError,
  FormGroup,
  Input,
  SaveButton,
  Select,
} from '../../../../_common/view/components';
import containerDi from '../../../../_common/view/di/container';
import { typesDi } from '../../../../_common/view/di/types-di';
import { errorHandler } from '../../../../_common/view/handlers/errorHandler';
import { keyboardControl } from '../../../../_common/view/handlers/keyboardControl';
import { ConfigService } from '../../../../_common/view/services';
import { TaxesRepository } from './repository/TaxesRepository';
import { getTitle } from '../../../../_common/view/handlers/functions';

let defaultModel: object = {};
const configService = containerDi.get<ConfigService>(typesDi.ConfigService);
const legacyUrl = configService.legacyUrl();

export const TaxesCard = () => {
  const repository = containerDi.get<TaxesRepository>(typesDi.TaxesRepository);
  const [model, setModel] = useState((document as any).data);
  const [userErrors, setErrors] = useState({});
  const [id, setId] = useState(Number(new URLSearchParams(window.location.search).get('id')));
  const [readonly, setReadonly] = useState(id > 0);
  const [isLoading, setIsLoading] = useState(false);
  const regions = (document as any).regions;
  const trans_categories = (document as any).trans_categories;
  const access: boolean = (document as any).access;
  const isAdmin: boolean = (document as any).is_admin;
  document.title = getTitle((document as any).title);
  if (Object.keys(defaultModel).length == 0) {
    defaultModel = { ...model };
  }

  const menuItems: Array<IMenuItem> = [
    {
      icon: IconConst.calc,
      title: 'Налоговая ставка',
      href: `/global-settings/taxes/`,
      id: 'taxes',
      params: { id: id },
    },
  ];
  if (id) {
    menuItems.push({
      icon: IconConst.changes,
      title: 'Правки',
      href: legacyUrl + `/car/tax/access?id=${id}`,
      id: 'access',
    });
  }

  const handleChange = ({ name, value }) => {
    setModel((prevState) => ({ ...prevState, [name]: value }));
  };

  const save = async (e) => {
    e.preventDefault();
    setIsLoading(true);
    try {
      await repository.saveCard(model).then((data) => {
        if (!id) {
          window.history.pushState('', '', `?id=${data.id}`);
          setId(data.id);
          setModel((prevState) => ({ ...prevState, id: data.id }));
        }
        setErrors({});
        defaultModel = { ...model };
        setReadonly(true);
        window.opener.postMessage('update_table', '*');
        setIsLoading(false);
      });
    } catch (e) {
      errorHandler(e, setErrors);
      setIsLoading(false);
    }
  };

  const deleteCard = async () => {
    if (confirm('Удалить налоговую ставку?')) {
      setIsLoading(true);
      try {
        await repository.deleteCard(id);
        window.opener.postMessage('update_table', '*');
        window.close();
      } catch (e) {
        errorHandler(e, setErrors);
        setIsLoading(false);
      }
    }
  };

  document.onkeyup = (e) => keyboardControl({ setModel, defaultModel, setReadonly, readonly, save, e });

  return (
    <div>
      <ContainerPage>
        <MenuHorizontal items={menuItems} />
        <ContainerForm>
          <div className="vspan0 scroll-y scroll-padding">
            <div className="form-horizontal label-large">
              <FormGroup label="Регион:">
                <Select
                  name="region"
                  value={model.region}
                  options={Object.values(regions)}
                  onChange={handleChange}
                  disabled={readonly}
                />
              </FormGroup>

              <FormGroup label="Налоговый год:">
                <AsNumberInput
                  name="year"
                  value={model.year}
                  onChange={handleChange}
                  readonly={readonly}
                  useGrouping={false}
                  minimumFractionDigits={0}
                  maxLength={4}
                />
              </FormGroup>

              <FormGroup label="Категория ТС по ИТР:">
                <Select
                  name="trans_category"
                  value={model.trans_category}
                  onChange={handleChange}
                  options={Object.values(trans_categories)}
                  disabled={readonly}
                />
              </FormGroup>

              <FormGroup label="Объект налогообложения:">
                <Input name="object" value={model.object} onChange={handleChange} readonly={readonly} />
              </FormGroup>

              <FormGroup label="Минимальное значение мощности:">
                <AsNumberInput
                  name="min_engine_power"
                  value={model.min_engine_power}
                  onChange={handleChange}
                  readonly={readonly}
                />
              </FormGroup>

              <FormGroup label="Максимальное значение мощности:">
                <AsNumberInput
                  name="max_engine_power"
                  value={model.max_engine_power}
                  onChange={handleChange}
                  readonly={readonly}
                />
              </FormGroup>

              <FormGroup label="Возраст ТС от, лет:">
                <AsNumberInput
                  name="car_age_from"
                  value={model.car_age_from}
                  onChange={handleChange}
                  readonly={readonly}
                  minimumFractionDigits={0}
                />
              </FormGroup>

              <FormGroup label="Возраст ТС до, лет:">
                <AsNumberInput
                  name="car_age_up_to"
                  value={model.car_age_up_to}
                  onChange={handleChange}
                  readonly={readonly}
                  minimumFractionDigits={0}
                />
              </FormGroup>

              <FormGroup label="Налоговая ставка, руб:">
                <AsNumberInput name="rate" value={model.rate} onChange={handleChange} readonly={readonly} />
              </FormGroup>
            </div>
          </div>
          <div className="buttons">
            <div className="pull-left">
              <DeleteButton access={isAdmin} readonly={readonly} onClick={deleteCard} />
            </div>
            <CloseButton
              readonly={readonly}
              setReadonly={setReadonly}
              setDefaultModel={setModel}
              defaultModel={defaultModel}
              isLoading={isLoading}
            />
            <SaveButton
              access={access}
              readonly={readonly && id > 0}
              setReadonly={setReadonly}
              onClick={save}
              isLoading={isLoading}
            />
          </div>
        </ContainerForm>
      </ContainerPage>
      <DivError errors={Object.values(userErrors)} setErrors={setErrors} />
    </div>
  );
};
