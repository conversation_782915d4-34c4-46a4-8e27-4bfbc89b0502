<?php

namespace modules\globalSettings\taxes\taxesImport\controllers;

use modules\_common\ItrController;
use Yii;

class IndexController extends ItrController
{
    /**
     * Возвращает файл шаблона импорта
     */
    public function actionGetTemplate(): void
    {
        $filePath = Yii::getAlias('@root') . '/libFiles/import/car_taxes.csv';

        Yii::$app->response->sendFile($filePath, null, ['inline' => true])->send();
    }
}
