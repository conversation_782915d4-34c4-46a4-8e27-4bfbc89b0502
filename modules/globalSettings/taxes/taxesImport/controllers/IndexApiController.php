<?php
namespace modules\globalSettings\taxes\taxesImport\controllers;

use backend\controllers\ApiController;
use backend\controllers\traits\ImportControllerTrait;
use backend\interfaces\ImportControllerInterface;
use backend\interfaces\ImportValidatorInterface;
use common\interfaces\ImportServiceInterface;
use common\services\ImportService;
use modules\globalSettings\taxes\index\services\TaxesService;
use modules\globalSettings\taxes\index\validators\SaveValidator;
use Yii;

class IndexApiController extends ApiController implements ImportControllerInterface
{
    use ImportControllerTrait;

    /**
     * Устанавливает список импортируемых полей
     */
    public function setColumns(): void
    {
        ImportService::$columns = [
            'region' => 'region',
            'year' => 'year',
            'trans_category' => 'trans_category',
            'object' => 'object',
            'min_engine_power' => 'min_engine_power',
            'max_engine_power' => 'max_engine_power',
            'car_age_from' => 'car_age_from',
            'car_age_up_to' => 'car_age_up_to',
            'rate' => 'rate',
        ];
    }

    /**
     * Возвращает имя импортируемого файла
     *
     * @return string
     */
    public function getFileName(): string
    {
        return 'car_taxes_tmp.csv';
    }

    /**
     * Возвращает экземпляр сервиса
     * @return ImportServiceInterface
     */
    public function getService(): ImportServiceInterface
    {
        return new TaxesService();
    }

    /**
     * Возвращает экземпляр валидатора
     *
     * @return ImportValidatorInterface
     */
    public function getSaveValidator(): ImportValidatorInterface
    {
        return new SaveValidator();
    }

    /**
     * Возвращает путь до файла импорта
     * @param  int  $firmId
     *
     * @return string
     */
    public function getFilePath(int $firmId): string
    {
        return Yii::getAlias('@root') . "/data/imports/$firmId/";
    }
}
