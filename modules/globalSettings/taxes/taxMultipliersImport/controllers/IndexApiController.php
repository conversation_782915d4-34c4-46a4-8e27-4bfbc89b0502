<?php
namespace modules\globalSettings\taxes\taxMultipliersImport\controllers;

use backend\controllers\ApiController;
use backend\controllers\traits\ImportControllerTrait;
use backend\interfaces\ImportControllerInterface;
use backend\interfaces\ImportValidatorInterface;
use common\interfaces\ImportServiceInterface;
use common\services\ImportService;
use modules\globalSettings\taxes\taxMultipliers\services\TaxMultipliersService;
use modules\globalSettings\taxes\taxMultipliers\validators\SaveValidator;
use Yii;

class IndexApiController extends ApiController implements ImportControllerInterface
{
    use ImportControllerTrait;

    public function setColumns(): void
    {
        ImportService::$columns = [
            'tax_year' => 'tax_year',
            'price_category' => 'price_category',
            'multiplier' => 'multiplier',
            'mark' => 'mark',
            'model' => 'model',
            'engine_type' => 'engine_type',
            'engine_volume' => 'engine_volume',
            'product_year_from' => 'product_year_from',
            'product_year_up_to' => 'product_year_up_to',
        ];
    }

    public function getFileName(): string
    {
        return 'tax_multipliers_tmp.csv';
    }

    /**
     * Возвращает путь до файла импорта
     * @param  int  $firmId
     *
     * @return string
     */
    public function getFilePath(int $firmId): string
    {
        return Yii::getAlias('@root') . "/data/imports/$firmId/";
    }

    public function getService(): ImportServiceInterface
    {
        return new TaxMultipliersService();
    }

    public function getSaveValidator(): ImportValidatorInterface
    {
        return new SaveValidator();
    }
}
