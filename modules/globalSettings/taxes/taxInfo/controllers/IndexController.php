<?php

namespace modules\globalSettings\taxes\taxInfo\controllers;

use common\constants\TransportCategories;
use Exception;
use modules\_common\ItrController;
use modules\globalSettings\index\services\GlobalSettingsService;
use yii\web\HttpException;

class IndexController extends ItrController
{
    /**
     * Рендер React-приложения
     *
     * @return string
     * @throws HttpException
     * @throws Exception
     */
    public function actionIndex(): string
    {
        GlobalSettingsService::access();
        $this->params['transports_categories'] = TransportCategories::getOptions();
        $this->setFirmParams();
        $this->setMainMenuAccess();
        $this->setRightMenuAccess();
        $this->setPageTitle([['title' => 'Справка']]);
        return $this->renderReact([]);
    }
}
