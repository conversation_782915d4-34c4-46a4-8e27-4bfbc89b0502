import { ContainerForm, ContainerTabPage } from '../../../../_common/view/layout';
import { GlobalSettingsMenu } from '../../../index/views/layout/GlobalSettingsMenu';
import { useEffect } from 'react';
import { getTitle } from '../../../../_common/view/handlers/functions';

export const TaxInfoPage = () => {
  const transport_categories = (document as any).transports_categories;
  useEffect(() => {
    document.title = getTitle((document as any).title);
  }, []);
  return (
    <ContainerTabPage>
      <GlobalSettingsMenu />
      <ContainerForm>
        <div className="container container-horizontal scroll-compact">
          <div className="hspan5">
            <div className="container-wrap scroll-y scroll-padding">
              <div className="container">
                <p>
                  Ставки транспортного налога, повышающих коэффициентов, налоговых льгот необходимо обновлять ежегодно.
                </p>
                <p>
                  Информация о размере ставок транспортного налога доступна на{' '}
                  <a href="https://www.nalog.gov.ru/rn77/service/tax/" target="_blank">
                    сайте ФНС.
                  </a>
                </p>
                <p>
                  При импорте налоговых ставок используйте шаблон.
                  <br />
                  ID категорий ТС:
                  <br />
                  {transport_categories &&
                    transport_categories.map((item: { key: number; value: string }) => (
                      <span key={item.key}>
                        {item.key} - {item.value} <br />
                      </span>
                    ))}
                </p>
                <p>
                  ID регионов:
                  <br />
                  5 - Республика Дагестан
                  <br />
                  20 - Чеченская Республика
                  <br />
                  50 - Московская область
                  <br />
                  77 - г. Москва
                  <br />
                  78 - г. Санкт-Петербург
                </p>
                <p>
                  Налоговый калькулятор можно найти{' '}
                  <a href="https://www.nalog.gov.ru/rn77/service/calc_transport/" target="_blank">
                    здесь.
                  </a>
                </p>
                <p>
                  Перечень легковых автомобилей, для которых установлены повышающие коэффициенты, можно найти {''}
                  <a href="https://minpromtorg.gov.ru/docs/list/" target="_blank">
                    на сайте Минпромторга РФ.
                  </a>
                </p>
                <p>При загрузке файла Минпромторга:</p>
                <ul className="block-counter">
                  <li>Замените написание марки “MercedesBenz” на “Mercedes-Benz”,</li>
                  <li>
                    Исправьте для соответствующих ТС тип двигателя на “электрический” и “гибридный”. Если тип двигателя
                    “любой”, создайте записи для каждого типа двигателя (бензин, дизель).
                  </li>
                  <li>
                    В мощности вместо “-” и “любой” укажите “-1”, для мощностей со знаками “/” или “()” создайте новые
                    записи.
                  </li>
                  <li>
                    Текстовое поле о возрасте ТС преобразуйте в{' '}
                    <a
                      href="https://docs.google.com/spreadsheets/d/1OIQPQl5-d03F55fxxVYR9GLIVm1RiGRnIvJYTZ65rDs/edit#gid=1778483476"
                      target="_blank"
                    >
                      год выпуска
                    </a>
                    .
                  </li>
                  <li>
                    Загружайте только те ТС, для которых повышающий коэффициент больше 1. Отправьте администратору
                    список моделей ТС Минпромторга нового года с повышающим коэффициентом больше 1, которых не было в
                    предыдущем году для добавления их в iTransport.
                  </li>
                </ul>

                <h2 className="center margin-bottom">
                  Повышающие коэффициенты в зависимости от стоимости и возраста ТС
                </h2>
                <table className="tax-info-table">
                  <thead>
                    <tr>
                      <th>Средняя розничная стоимость ТС, руб.</th>
                      <th>Прошло с года выпуска лет, не более</th>
                      <th>Коэффициент</th>
                    </tr>
                  </thead>
                  <tbody>
                    <tr>
                      <td>От 3 000 000 до 5 000 000</td>
                      <td>3</td>
                      <td>1,1</td>
                    </tr>
                    <tr>
                      <td>От 5 000 000,01 до 10 000 000</td>
                      <td>5</td>
                      <td>2</td>
                    </tr>
                    <tr>
                      <td>От 10 000 000,01 до 15 000 000</td>
                      <td>10</td>
                      <td>3</td>
                    </tr>
                    <tr>
                      <td>от 15 000 000,01</td>
                      <td>20</td>
                      <td>3</td>
                    </tr>
                  </tbody>
                </table>
                <p>
                  <b>За 2022 год повышающий коэффициент применяется только для ТС стоимостью от 10 млн.руб.</b>
                </p>
              </div>
            </div>
          </div>
        </div>
      </ContainerForm>
    </ContainerTabPage>
  );
};
