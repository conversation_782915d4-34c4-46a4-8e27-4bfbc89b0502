<?php

namespace modules\globalSettings\taxes;

use modules\_common\ItrModule;
use modules\globalSettings\taxes\taxBenefits\TaxBenefitsModule;
use modules\globalSettings\taxes\taxesImport\TaxesImportModule;
use modules\globalSettings\taxes\taxInfo\TaxInfoModule;
use modules\globalSettings\taxes\taxMultipliers\TaxMultipliersModule;
use modules\globalSettings\taxes\taxMultipliersImport\TaxMultipliersImportModule;

class TaxesModule extends ItrModule
{
    public $layout = 'main';
    public $controllerNamespace = 'modules\globalSettings\taxes\index\controllers';

    public function init(): void
    {
        parent::init();
        $this->modules = [
            'tax-multipliers' => [
                'class' => TaxMultipliersModule::class,
            ],
            'tax-benefits' => [
                'class' => TaxBenefitsModule::class,
            ],
            'taxes-import' => [
                'class' => TaxesImportModule::class,
            ],
            'tax-multipliers-import' => [
                'class' => TaxMultipliersImportModule::class,
            ],
            'tax-info' => [
                'class' => TaxInfoModule::class,
            ],
        ];
    }
}
