import { ChangeIdCard, ContainerForm, ContainerPage } from '../../../_common/view/layout';
import React, { useEffect, useRef, useState } from 'react';
import {
  AsNumberInput,
  CloseButton,
  DeleteButton,
  DivError,
  FormGroup,
  Input,
  SaveButton,
} from '../../../_common/view/components';
import { handleChange } from '../../../_common/view/handlers/changeHandlers';
import { MenuHorizontal } from '../../../_common/view/menu';
import { IMenuItem } from '../../../_common/view/interfaces';
import { IconConst } from '../../../_common/view/const';
import { legacyUrl } from '../../../../conf';
import containerDi from '../../../_common/view/di/container';
import { typesDi } from '../../../_common/view/di/types-di';
import { CurrencyRepository } from './repository/CurrencyRepository';
import { errorHandler } from '../../../_common/view/handlers/errorHandler';
import { getTitle } from '../../../_common/view/handlers/functions';

export const CurrencyCard = () => {
  const repository = containerDi.get<CurrencyRepository>(typesDi.CurrencyRepository);

  const [errors, setErrors] = useState({});
  const [id, setId] = useState(Number(new URLSearchParams(window.location.search).get('id')));
  const [readonly, setReadonly] = useState(id > 0);
  const [model, setModel] = useState((document as any).data ?? {});
  const [isLoading, setIsLoading] = useState(false);
  const defaultModel = useRef({ ...model });
  const isAdmin = (document as any).is_admin;

  const menuItems: Array<IMenuItem> = [
    {
      icon: IconConst.currency,
      title: 'Валюта',
      href: '/global-settings/currencies/card/',
      params: { id: id },
    },
  ];
  if (id > 0) {
    menuItems.push({
      icon: IconConst.changes,
      title: 'Правки',
      href: legacyUrl + '/global-settings/currencies/card/access',
      params: { id: id },
    });
  }

  const saveCard = async (e) => {
    e.preventDefault();
    setIsLoading(true);
    try {
      await repository.saveCard(model).then((data) => {
        if (id == 0) {
          window.history.pushState('', '', `/global-settings/currencies/card/?id=${data.id}`);
          setId(data.id);
          setModel((prevState) => ({ ...prevState, id: data.id }));
        }
        setErrors({});
        setModel(data);
        defaultModel.current = { ...model };
        setReadonly(true);
        window?.opener?.getTable();
        setIsLoading(false);
      });
    } catch (e) {
      errorHandler(e, setErrors);
      setIsLoading(false);
    }
  };

  const deleteCard = async (e) => {
    e.preventDefault();
    if (confirm('Удалить валюту?')) {
      setIsLoading(true);
      try {
        await repository.deleteCard(id).then(() => {
          window?.opener?.getTable();
          window.close();
        });
      } catch (e) {
        errorHandler(e, setErrors);
        setIsLoading(false);
      }
    }
  };

  useEffect(() => {
    handleChange({ name: 'is_new', value: id == 0, setModel });
    document.title = getTitle(document.title);
  }, [id]);

  return (
    <div className="desktop">
      <ContainerPage>
        <MenuHorizontal items={menuItems}>
          <ChangeIdCard />
        </MenuHorizontal>
        <ContainerForm>
          <div className="vspan0 scroll-y scroll-padding">
            <div className="form-horizontal">
              <FormGroup label="Цифровой код:">
                <AsNumberInput
                  useGrouping={false}
                  minimumFractionDigits={0}
                  maxLength={3}
                  name="id"
                  value={model.id}
                  readonly={readonly}
                  onChange={({ name, value }) => handleChange({ name, value, setModel })}
                />
              </FormGroup>
              <FormGroup label="Буквенный код:">
                <Input
                  name="char_code"
                  value={model.char_code}
                  readonly={readonly}
                  maxLength={3}
                  onChange={({ name, value }) => handleChange({ name, value, setModel })}
                />
              </FormGroup>
              <FormGroup label="Наименование:">
                <Input
                  name="name"
                  value={model.name}
                  readonly={readonly}
                  onChange={({ name, value }) => handleChange({ name, value, setModel })}
                />
              </FormGroup>
            </div>
          </div>
          <div className="buttons">
            <div className="pull-left">
              <DeleteButton access={isAdmin} readonly={readonly && id} onClick={deleteCard} />
            </div>
            <CloseButton
              readonly={readonly || id == 0}
              setReadonly={setReadonly}
              setDefaultModel={setModel}
              defaultModel={defaultModel.current}
              isLoading={isLoading}
            />
            <SaveButton
              access={true}
              readonly={readonly}
              setReadonly={setReadonly}
              onClick={saveCard}
              isLoading={isLoading}
            />
          </div>
        </ContainerForm>
      </ContainerPage>
      <DivError errors={Object.values(errors)} setErrors={setErrors} />
    </div>
  );
};
