import { AddButton, UpdateButton } from '../../../_common/view/layout/buttons';
import { IconConst, windowConst } from '../../../_common/view/const';
import { DivError } from '../../../_common/view/components';
import { ContainerTabPage } from '../../../_common/view/layout';
import React, { useEffect, useState } from 'react';
import { errorHandler } from '../../../_common/view/handlers/errorHandler';
import { getTitle } from '../../../_common/view/handlers/functions';
import { IMenuItem, ITableHeader, ITableRow } from '../../../_common/view/interfaces';
import containerDi from '../../../_common/view/di/container';
import { CurrencyRepository } from './repository/CurrencyRepository';
import { typesDi } from '../../../_common/view/di/types-di';
import { DataGrid } from '../../../_common/view/layout/DataGrid';
import { GlobalSettingsMenu } from '../../index/views/layout/GlobalSettingsMenu';

export const CurrenciesPage = () => {
  const repository = containerDi.get<CurrencyRepository>(typesDi.CurrencyRepository);

  const [isLoading, setIsLoading] = useState(false);
  const [rows, setRows] = useState([]);
  const [errors, setErrors] = useState({});
  const access = (document as any).access;

  const menuItems: Array<IMenuItem> = [
    {
      icon: IconConst.currency,
      title: 'Валюты',
      href: '/global-settings/currencies/',
    },
  ];

  const header: Array<ITableHeader> = [
    { value: 'Код', width: 70, name: 'code' },
    { value: 'Букв. код', width: 70, name: 'naming_code' },
    { value: 'Наименование', width: 200, name: 'name' },
    { value: '', width: 'auto', name: '' },
  ];

  const prepareCurrencies = (rows) => {
    return rows.map((row): ITableRow => {
      return {
        id: row.id,
        columns: [
          { value: row.id, name: 'id' },
          { value: row.char_code, name: 'char_code' },
          { value: row.name, name: 'name' },
          { value: '', name: '' },
        ],
        checkboxSelect: false,
      };
    });
  };

  const getTable = async (data = {}) => {
    try {
      await setIsLoading(true);
      let response: ITableRow[] = await repository.getTable();
      let preparedRows = await prepareCurrencies(response);
      await setRows(preparedRows);
      await setIsLoading(false);
    } catch (e) {
      await setIsLoading(false);
      errorHandler(e, setErrors);
    }
  };

  useEffect(() => {
    getTable();
    document.title = getTitle(document.title);
    (window as any).getTable = getTable;
  }, []);

  return (
    <ContainerTabPage>
      <GlobalSettingsMenu />
      <div className="vspan0 container-form">
        <div className="container-wrap">
          <div className="container">
            <ul className="toolbar-filter center"></ul>
            <ul className="toolbar">
              <li>
                <UpdateButton onClick={() => getTable()} />
              </li>
              <li>
                <AddButton window={windowConst.currency} />
              </li>
            </ul>
            <div className="vspan0">
              <DataGrid
                setRows={setRows}
                rows={rows}
                header={header}
                footer={{ count: 50 }}
                isLoading={isLoading}
                resizeable={false}
                onDoubleClick={windowConst.currency}
              />
              <DivError setErrors={setErrors} errors={Object.values(errors)} />
            </div>
          </div>
        </div>
      </div>
    </ContainerTabPage>
  );
};
