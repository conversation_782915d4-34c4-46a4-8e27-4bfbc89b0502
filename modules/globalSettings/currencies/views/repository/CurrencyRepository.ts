import { inject, injectable } from 'inversify';
import { typesDi } from '../../../../_common/view/di/types-di';
import { HttpService } from '../../../../_common/view/services';

@injectable()
export class CurrencyRepository {
  constructor(@inject(typesDi.HttpService) private httpService: HttpService) {}

  async saveCard(data: any) {
    return await this.httpService.post('/global-settings/currencies/index-api/save/', data);
  }

  async deleteCard(id: number) {
    return await this.httpService.post('/global-settings/currencies/index-api/delete/', { id: id });
  }

  async getTable() {
    return await this.httpService.post('/global-settings/currencies/index-api/get-table/', {});
  }
}
