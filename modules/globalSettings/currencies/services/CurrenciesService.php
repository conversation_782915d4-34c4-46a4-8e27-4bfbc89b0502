<?php
namespace modules\globalSettings\currencies\services;

use common\models\CurrenciesModel;
use modules\globalSettings\currencies\validators\CurrencySaveValidator;
use yii\db\StaleObjectException;
use yii\web\HttpException;

class CurrenciesService
{
    /**
     * Получение данных карточки валюты
     * @throws HttpException
     */
    public static function getCard(?int $id): array
    {
        if (!$id) {
            $currency = new CurrenciesModel();
        } else {
            $currency = CurrenciesModel::findOne(['id' => $id]);
        }
        if (!$currency) {
            throw new HttpException(404);
        }
        return $currency->attributes;
    }

    /**
     * Сохранение карточки валюты
     * @param  CurrencySaveValidator  $validator
     *
     * @return int
     */
    public static function saveCard(CurrencySaveValidator $validator): int
    {
        $currency = CurrenciesModel::findOne(['id' => $validator->id]);
        if (!$currency) {
            $currency = new CurrenciesModel();
        }
        $currency->setAttributes($validator->attributes, false);
        $currency->save();
        return $currency->id;
    }

    /**
     * Удаление карточки валюты
     * @param  int  $id
     *
     * @throws HttpException
     * @throws \Throwable
     * @throws StaleObjectException
     */
    public static function deleteCard(int $id): void
    {
        $currency = CurrenciesModel::findOne(['id' => $id]);
        if (!$currency) {
            throw new HttpException(404);
        }
        $currency->delete();
    }
}
