<?php
namespace modules\globalSettings\currencies\controllers;

use modules\_common\ItrController;
use modules\globalSettings\currencies\services\CurrenciesService;
use modules\globalSettings\index\services\GlobalSettingsService;
use Yii;
use yii\web\HttpException;

class CardController extends ItrController
{
    /**
     * Карточка Валюта
     * @throws HttpException
     */
    public function actionIndex(): string
    {
        GlobalSettingsService::access();
        $this->error404Handler = 'error404Card';
        $code = Yii::$app->request->get('id', 0);
        $this->setFirmParams();
        $this->setPageTitle([['title' => 'Валюта', 'id' => $code]]);
        return $this->renderReact(CurrenciesService::getCard($code));
    }
}
