<?php

namespace modules\globalSettings\currencies\controllers;

use modules\_common\ItrController;
use modules\globalSettings\index\services\GlobalSettingsService;
use yii\web\HttpException;

class IndexController extends ItrController
{
    /**
     * Страница Валюты
     * @throws HttpException
     */
    public function actionIndex(): string
    {
        GlobalSettingsService::access();
        $this->setMainMenuAccess();
        $this->setRightMenuAccess();
        $this->setFirmParams();
        $this->setPageTitle([['title' => 'Валюты']]);
        return $this->renderReact([]);
    }
}
