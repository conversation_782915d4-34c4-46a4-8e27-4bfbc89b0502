<?php

namespace modules\globalSettings\currencies\controllers;

use backend\controllers\ApiController;
use backend\validators\common\ValidatorHelpers;
use common\models\CurrenciesModel;
use common\types\UserRolesTypes;
use modules\access\services\UserService;
use modules\globalSettings\currencies\services\CurrenciesService;
use modules\globalSettings\currencies\validators\CurrencySaveValidator;
use modules\globalSettings\index\services\GlobalSettingsService;
use Yii;
use yii\db\StaleObjectException;
use yii\web\HttpException;

class IndexApiController extends ApiController
{
    /**
     * Получение таблицы
     * @throws HttpException
     */
    public function actionGetTable(): array
    {
        GlobalSettingsService::access();
        return CurrenciesModel::find()
            ->orderBy('id ASC')
            ->asArray()
            ->all() ?? [];
    }

    /**
     * Сохранение карточки валюты
     * @throws HttpException
     */
    public function actionSave(): array
    {
        /** @var CurrencySaveValidator $validator */
        GlobalSettingsService::access();
        $validator = ValidatorHelpers::createFromPost(CurrencySaveValidator::class);
        if (!$validator->validate()) {
            return $this->getReactItrErrors($validator);
        }
        $code = CurrenciesService::saveCard($validator);
        return CurrenciesService::getCard($code);
    }

    /**
     * Удаление карточки валюты
     * @throws HttpException
     * @throws \Throwable
     * @throws StaleObjectException
     */
    public function actionDelete(): void
    {
        GlobalSettingsService::access();
        if (!UserService::hasRole(UserRolesTypes::ADMIN) && !UserService::hasRole(UserRolesTypes::HELPER)) {
            throw new HttpException(403);
        }
        $id = Yii::$app->request->post('id');
        CurrenciesService::deleteCard($id);
    }
}
