<?php
namespace modules\globalSettings\currencies\validators;

use backend\validators\common\DuplicateValidator;
use backend\validators\common\NumberValidator;
use common\models\CurrenciesModel;
use yii\base\Model;

class CurrencySaveValidator extends Model
{
    public $id;
    public $char_code;
    public $name;
    public $is_new;

    public function rules(): array
    {
        return [
            [['char_code', 'name'], 'required'],
            [
                ['id'],
                'required',
                'isEmpty' => function ($value) {
                    return !$value;
                },
            ],
            [['id'], NumberValidator::class],
            [['char_code', 'name'], 'string'],
            [
                ['id'],
                DuplicateValidator::class,
                'targetClass' => CurrenciesModel::class,
                'targetAttribute' => 'id',
                'when' => function () {
                    return (bool) $this->is_new;
                },
            ],
            [
                ['id', 'char_code'],
                function ($attribute) {
                    $label = $this->getAttributeLabel($attribute) ?: $attribute;
                    if (strlen($this->$attribute) < 3 || strlen($this->$attribute) > 3) {
                        $this->addError($attribute, "$label должен состоять из 3 символов");
                    }
                },
            ],
            [
                ['char_code'],
                function () {
                    $this->char_code = strtoupper($this->char_code);
                },
            ],
        ];
    }

    public function attributeLabels(): array
    {
        return [
            'id' => 'Цифровой код',
            'char_code' => 'Символьный код',
            'name' => 'Наименование',
        ];
    }
}
