<?php
namespace modules\documentFlow\validators;

use yii\base\Model;

/**
 * @property $firm
 * @property $real_name
 * @property $id
 */

class SaveDocumentValidator extends Model
{
    public $real_name;
    public $id;
    public $deliverable;

    public function rules(): array
    {
        return [[['real_name'], 'required'], [['deliverable'], 'default', 'value' => 0]];
    }

    public function attributeLabels(): array
    {
        return [
            'real_name' => 'Наименование',
        ];
    }
}
