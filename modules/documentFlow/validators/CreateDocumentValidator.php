<?php
namespace modules\documentFlow\validators;

use backend\validators\common\NumberValidator;
use yii\base\Model;

class CreateDocumentValidator extends Model
{
    public $invoice_id;
    public $firm;
    public $deliverable;

    public function rules(): array
    {
        return [
            [['invoice_id', 'firm'], 'required'],
            [['deliverable'], 'default', 'value' => false],
            [['invoice_id', 'firm'], NumberValidator::class],
        ];
    }
}
