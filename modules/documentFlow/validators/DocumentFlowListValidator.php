<?php
namespace modules\documentFlow\validators;

use yii\base\Model;

class DocumentFlowListValidator extends Model
{
    public $start;
    public $end;
    public $delivered;
    public $send;
    public $deliverable;
    public $page;
    public $limit;
    public $type;
    public $recipient_type;
    public $recipient;
    public $sender;

    public function rules(): array
    {
        return [
            [['page'], 'default', 'value' => 0],
            [['limit'], 'default', 'value' => 100],
            [['start'], 'default', 'value' => ''],
            [['end'], 'default', 'value' => ''],
            [['delivered', 'type', 'deliverable', 'send'], 'default', 'value' => []],
        ];
    }
}
