<?php

namespace modules\documentFlow\controllers;

use backend\controllers\ApiController;
use backend\validators\common\GetOptionsValidator;
use backend\validators\common\ValidatorHelpers;
use common\services\AutoParkService;
use Exception;
use modules\documentFlow\services\DocumentFlowService;
use modules\documentFlow\validators\CreateDocumentValidator;
use modules\documentFlow\validators\DocumentFlowListValidator;
use modules\documentFlow\validators\SaveDocumentValidator;
use Yii;
use yii\web\HttpException;

final class IndexApiController extends ApiController
{
    /**
     * Получение списка документов
     * @return array[]
     * @throws Exception
     * @api
     */
    public function actionIndex(): array
    {
        DocumentFlowService::access();
        $validator = ValidatorHelpers::createFromPost(DocumentFlowListValidator::class);
        if (!$validator->validate()) {
            return $this->getReactItrErrors($validator);
        }
        return DocumentFlowService::getList($validator);
    }

    /**
     * Сохранение документа
     * @throws Exception
     */
    public function actionSave(): array
    {
        $validator = ValidatorHelpers::createFromPost(SaveDocumentValidator::class);
        if (!$validator->validate()) {
            return $this->getReactItrErrors($validator);
        }
        $id = DocumentFlowService::saveCard($validator);
        return DocumentFlowService::getCard($id);
    }

    /**
     * Получение пагинации
     * @return array
     * @throws HttpException
     * @throws Exception
     * @api
     */
    public function actionPagination(): array
    {
        DocumentFlowService::access();
        $validator = ValidatorHelpers::createFromPost(DocumentFlowListValidator::class);
        if (!$validator->validate()) {
            return $this->getReactItrErrors($validator);
        }
        return DocumentFlowService::getPagination($validator);
    }

    /**
     * Получение опций получателя
     * @return array
     * @throws Exception
     * @api
     */
    public function actionGetRecipientOptions(): array
    {
        $recipientType = Yii::$app->request->post('recipient_type') ?? '';
        return DocumentFlowService::getRecipientOptions($recipientType);
    }

    /**
     * Получение списка документов для выпадающего списка
     * TODO в контроллер email, когда он будет переписан
     * @return string[][]
     * @throws Exception
     * @api
     */
    public function actionGetOptions(): array
    {
        $post = Yii::$app->request->post();
        $validator = new GetOptionsValidator();
        $validator->setAttributes($post, false);
        if (!$validator->validate()) {
            return ['errors' => ['message' => $validator->errors]];
        }
        AutoParkService::getInstance()->setCurrentPark($validator->firm);
        return DocumentFlowService::getOptions();
    }

    /**
     * Создание документа
     * @throws Exception
     * @return array
     * @api
     */
    public function actionCreateDocument(): array
    {
        $post = Yii::$app->request->post();
        $validator = new CreateDocumentValidator();
        $validator->setAttributes($post, false);
        if (!$validator->validate()) {
            return ['errors' => ['message' => $validator->errors]];
        }
        AutoParkService::getInstance()->setCurrentPark($validator->firm);
        return DocumentFlowService::create($validator);
    }
}
