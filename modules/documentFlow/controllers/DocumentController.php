<?php

namespace modules\documentFlow\controllers;

use backend\validators\common\IdValidator;
use backend\validators\common\ValidatorHelpers;
use common\models\FilesModel;
use modules\_common\ItrController;
use modules\_common\services\UsersCommonService;
use modules\documentFlow\services\DocumentFlowService;
use yii\web\HttpException;
use yii\web\NotFoundHttpException;

/**
 * @api
 */
class DocumentController extends ItrController
{
    /**
     * Карточка документа
     * @throws HttpException
     */
    public function actionIndex(): string
    {
        DocumentFlowService::access();
        $this->setFirmParams();
        $validator = ValidatorHelpers::createFromGet(IdValidator::class);
        $validator->validate();
        $this->setPageTitle([['title' => 'Документ', 'id' => $validator->id]]);
        return $this->renderReact(DocumentFlowService::getCard($validator->id));
    }

    /**
     * Правки
     * @throws HttpException
     * @throws \Exception
     * @api
     */
    public function actionChanges(): string
    {
        DocumentFlowService::access();
        $this->setFirmParams();
        $id = $this->request->get('id') ?? 0;
        $this->setPageTitle([['title' => 'Документ', 'id' => $id], ['title' => 'Правки']]);
        $file = FilesModel::findOneSelect(['id' => $id], ['id']);
        if (!$file) {
            throw new NotFoundHttpException('Документ не найден');
        }
        $this->params['author_options'] = [
            ['key' => 0, 'value' => 'Неизвестно'],
            ...UsersCommonService::getUsersOptions(),
        ];
        $this->params['owner_id'] = $id;
        return $this->renderReact();
    }
}
