<?php

namespace modules\documentFlow\controllers;

use backend\validators\common\ValidatorHelpers;
use common\models\FilialsModel;
use Exception;
use modules\_common\ItrController;
use modules\documentFlow\services\DocumentFlowService;
use modules\documentFlow\validators\DocumentFlowListValidator;
use yii\web\HttpException;

final class IndexController extends ItrController
{
    /**
     * Страница Документооборот
     * @throws HttpException
     * @throws Exception
     */
    public function actionIndex(): string
    {
        DocumentFlowService::access();
        $this->setFirmParams();
        $this->setRightMenuAccess();
        $this->setMainMenuAccess();
        $this->params['recipient_options'] = DocumentFlowService::getRecipientOptions('');
        $this->params['sender_options'] = [...[['key' => 0, 'value' => '(все)']], ...FilialsModel::getOptions()];
        $this->setPageTitle([['title' => 'Документы']]);
        return $this->renderReact();
    }

    /**
     * Открыть в Excel
     * @return string
     * @throws HttpException
     * @throws Exception
     */
    public function actionExcel(): string
    {
        DocumentFlowService::access();
        $validator = ValidatorHelpers::createFromGetJson(DocumentFlowListValidator::class);
        if (!$validator->validate()) {
            throw new HttpException(500);
        }
        $response = DocumentFlowService::getList($validator);
        return $this->renderExcel('/documentFlow', ['documents' => $response], 'document_flow');
    }
}
