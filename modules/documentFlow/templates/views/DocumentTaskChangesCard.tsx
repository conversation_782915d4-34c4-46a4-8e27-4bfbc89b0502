import { ChangesStore } from '../../../_common/view/store/ChangesStore';
import containerDi from '../../../_common/view/di/container';
import { typesDi } from '../../../_common/view/di/types-di';
import { ChangesTable } from '../../../_common/view/components/ChangesTable/ChangesTable';
import { getIdFromUrl } from '../../../_common/view/handlers/functions';
import { useEffect } from 'react';
import { observer } from 'mobx-react-lite';
import { DocumentTaskChangesRepository } from './repositories/DocumentTaskChangesRepository';
import { DocumentTaskCardMenu } from './components/DocumentTaskCardMenu';

const store: ChangesStore = containerDi.get<ChangesStore>(typesDi.ChangesStore);

export const DocumentTaskChangesCard = observer(() => {
  useEffect((): void => {
    store.changesRepository = containerDi.get<DocumentTaskChangesRepository>(typesDi.DocumentTaskChangesRepository);
    store.init();
  }, []);

  if (!store.isInitiate) return <></>;
  return <ChangesTable store={store} menu={<DocumentTaskCardMenu id={getIdFromUrl()} />} />;
});
