import { IChangesRepository } from '../../../../_common/view/components/ChangesTable/interfaces/IChangesRepository';
import { typesDi } from '../../../../_common/view/di/types-di';
import { HttpService } from '../../../../_common/view/services';
import { IChangesDifferentRow } from '../../../../_common/view/components/ChangesTable/interfaces/IChangesDifferentRow';
import { IChangesQueryParams } from '../../../../_common/view/components/ChangesTable/interfaces/IChangesQueryParams';
import { IChangesNavigationRow } from '../../../../_common/view/components/ChangesTable/interfaces/IChangesNavigationRow';
import { IChangesTableGroup } from '../../../../_common/view/components/ChangesTable/interfaces/IChangesTableGroup';
import { IPaginationResponse } from '../../../../_common/view/interfaces';
import { inject, injectable } from 'inversify';
import { IChangeQueryParams } from '../../../../_common/view/components/ChangesTable/interfaces/IChangeQueryParams';

@injectable()
export class DocumentTaskChangesRepository implements IChangesRepository {
  constructor(@inject(typesDi.HttpService) private httpService: HttpService) {}

  async getDifferentTable(data: IChangeQueryParams): Promise<Array<IChangesDifferentRow>> {
    return await this.httpService.post('/document-flow/templates/changes-api/changes/', data);
  }
  async getNavigationTable(data: IChangesQueryParams): Promise<Array<IChangesNavigationRow>> {
    return await this.httpService.post('/document-flow/templates/changes-api/navigation-table/', data);
  }

  async getTable(data: IChangesQueryParams): Promise<Array<IChangesTableGroup>> {
    return await this.httpService.post('/document-flow/templates/changes-api/', data);
  }

  async getPagination(data: IChangesQueryParams): Promise<IPaginationResponse> {
    return await this.httpService.post('/document-flow/templates/changes-api/pagination/', data);
  }
}
