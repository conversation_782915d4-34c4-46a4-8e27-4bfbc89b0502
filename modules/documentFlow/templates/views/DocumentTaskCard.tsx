import { ContainerForm, ContainerPage, UploadFilesModal } from '../../../_common/view/layout';
import { observer } from 'mobx-react-lite';
import { DocumentTaskCardStore } from './store/DocumentTaskCardStore';
import containerDi from '../../../_common/view/di/container';
import { typesDi } from '../../../_common/view/di/types-di';
import { useEffect } from 'react';
import { DocumentTaskCardMenu } from './components/DocumentTaskCardMenu';
import { getIdFromUrl } from '../../../_common/view/handlers/functions';
import {
  CheckboxWithLabel,
  DivError,
  Divider,
  DropInput,
  FormGroup,
  Input,
  InputDateTime,
  MultiCheckbox,
  Select,
} from '../../../_common/view/components';
import { UploadDocumentTemplateButton } from './components/UploadDocumentTemplateButton';
import { HrefTargetConst } from '../../../_common/view/const';
import { ButtonsBlock } from './components/ButtonsBlock';
import { FinAccountTypeOptions } from '../../../management/common/views/constants/FinAccountTypeOptions';
import { OwnerTypes } from '../../../_common/view/types';

const store: DocumentTaskCardStore = containerDi.get<DocumentTaskCardStore>(typesDi.DocumentTaskCardStore);

export const DocumentTaskCard = observer(() => {
  useEffect(() => {
    store.init();
  }, []);

  if (!store.isInitiate) return <></>;

  return (
    <div>
      <ContainerPage>
        <DocumentTaskCardMenu id={getIdFromUrl()} />
        <ContainerForm inactive={!store.defaultModel.enabled && getIdFromUrl() > 0}>
          <div className="vspan0 scroll-y scroll-padding">
            <div className="form-horizontal">
              <FormGroup label="Дата создания">
                <Input className="w200" value={store.model.date_create} readonly={true} />
              </FormGroup>
              <FormGroup label="Тип">
                <Select
                  className="w200"
                  name="type"
                  value={store.model.type}
                  options={store.typeOptions}
                  onChange={store.changeHandler}
                  disabled={store.readonly}
                />
              </FormGroup>
              <FormGroup label="Включен">
                <CheckboxWithLabel
                  name="enabled"
                  value={store.model.enabled}
                  onChange={store.checkboxChangeHandler}
                  readonly={store.readonly}
                >
                  Да
                </CheckboxWithLabel>
              </FormGroup>
              <FormGroup label="Автоматически" title="Документ по этому шаблону будет создан автоматически">
                <CheckboxWithLabel
                  name="auto"
                  value={store.model.auto}
                  onChange={store.checkboxChangeHandler}
                  readonly={store.readonly}
                >
                  Да
                </CheckboxWithLabel>
              </FormGroup>
              <FormGroup label="Отправлять" title="Отправлять созданный документ на электронную почту плательщика">
                <CheckboxWithLabel
                  name="deliverable"
                  value={store.model.deliverable}
                  onChange={store.checkboxChangeHandler}
                  readonly={store.readonly}
                >
                  Да
                </CheckboxWithLabel>
              </FormGroup>
              <FormGroup label="Проекты">
                <MultiCheckbox
                  className="w200"
                  DropButton={DropInput}
                  caption="Проекты"
                  name="projects"
                  values={store.model.projects}
                  options={store.projectOptions}
                  onChange={store.multiCheckboxChangeHandler}
                  readonly={store.readonly}
                />
              </FormGroup>
              <FormGroup label="Статьи">
                <MultiCheckbox
                  className="w200"
                  DropButton={DropInput}
                  caption="Статьи"
                  name="clauses"
                  values={store.model.clauses}
                  options={store.clauseOptions}
                  onChange={store.multiCheckboxChangeHandler}
                  readonly={store.readonly}
                />
              </FormGroup>
              <FormGroup label="Тип плательщика">
                <Select
                  className="w200 margin-right-small"
                  name="acc_source_owner"
                  value={store.model.acc_source_owner}
                  options={FinAccountTypeOptions}
                  onChange={store.changeHandler}
                  disabled={store.readonly}
                  dataEmpty={true}
                />
                {store.enableDriverTrusted && store.model.acc_source_owner == OwnerTypes.DRIVER && (
                  <MultiCheckbox
                    className="w200"
                    DropButton={DropInput}
                    caption="Свои/Не свои"
                    name="acc_source_owner_group"
                    values={store.model.acc_source_owner_group}
                    options={store.driverTrustedOptions}
                    onChange={store.multiCheckboxChangeHandler}
                    readonly={store.readonly}
                  />
                )}
              </FormGroup>
              <FormGroup label="Тип получателя">
                <Select
                  className="w200 margin-right-small"
                  name="acc_dest_owner"
                  value={store.model.acc_dest_owner}
                  options={FinAccountTypeOptions}
                  onChange={store.changeHandler}
                  disabled={store.readonly}
                  dataEmpty={true}
                />
                {store.model.acc_dest_owner == OwnerTypes.FILIAL && (
                  <MultiCheckbox
                    className="w200"
                    DropButton={DropInput}
                    caption="Получатели"
                    name="acc_dest_owners"
                    values={store.model.acc_dest_owners}
                    options={store.destOwnerOptions}
                    onChange={store.multiCheckboxChangeHandler}
                    readonly={store.readonly}
                  />
                )}
              </FormGroup>
              <Divider />
              <FormGroup label="Начиная с">
                <InputDateTime
                  className="w200"
                  name="start_time"
                  value={store.model.start_time}
                  onChange={store.changeHandler}
                  readonly={store.readonly}
                />
              </FormGroup>
              <FormGroup label="Заканчивая">
                <>
                  <InputDateTime
                    className="w200"
                    name="end_time"
                    value={store.model.end_time}
                    onChange={store.changeHandler}
                    readonly={store.readonly}
                  />{' '}
                  <span>Пустое поле - бессрочно</span>
                </>
              </FormGroup>
              <FormGroup label="Следующее">
                <InputDateTime
                  className="w200"
                  name="next_time"
                  value={store.model.next_time}
                  onChange={store.changeHandler}
                  readonly={store.readonly}
                />
              </FormGroup>
              <FormGroup label="Шаблон">
                <>
                  <UploadDocumentTemplateButton
                    onClick={() => store.setModalVisible(true)}
                    disabled={!store.readonly}
                  />{' '}
                  <span>Загрузите шаблон после сохранения задания</span>
                </>
              </FormGroup>
              <FormGroup label="Название шаблона">
                {Boolean(store.model.template_name) && (
                  <a href={store.model.template_link} target={HrefTargetConst._blank}>
                    {store.model.template_name}
                  </a>
                )}
              </FormGroup>
              {store.model.id > 0 && !Boolean(store.model.template_name) && (
                <div className="red fs20">Данное задание не выполняется без загруженного шаблона</div>
              )}
            </div>
          </div>
          <ButtonsBlock store={store} />
        </ContainerForm>
      </ContainerPage>
      <DivError errors={Object.values(store.errors)} setErrors={store.setErrors} />
      <UploadFilesModal
        uploadFileName="FileUploadValidator[file]"
        fileTypes={[{ key: 0, value: 'Шаблон' }]}
        setErrors={store.setErrors}
        setHidden={() => store.setModalVisible(false)}
        submit={store.upload}
        hidden={!store.modalVisible}
        ownerId={store.model.id}
        dragActive={false}
        multiple={false}
      />
    </div>
  );
});
