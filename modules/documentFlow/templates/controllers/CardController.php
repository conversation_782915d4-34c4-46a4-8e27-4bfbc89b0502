<?php

namespace modules\documentFlow\templates\controllers;

use common\models\DocumentTemplatesModel;
use common\models\FilialsModel;
use common\models\ProjectsModel;
use common\services\AccessService;
use common\types\ClausesTypes;
use common\types\DocumentFileExtensionsTypes;
use common\types\DocumentTemplateType;
use common\types\DriverTrustedTypes;
use Exception;
use modules\_common\ItrController;
use modules\_common\services\UsersCommonService;
use modules\documentFlow\services\DocumentFlowService;
use modules\documentFlow\templates\services\TemplatesService;
use yii\web\HttpException;
use yii\web\NotFoundHttpException;

class CardController extends ItrController
{
    /**
     * Карточка шаблона
     * @throws HttpException
     * @throws Exception
     */
    public function actionIndex(): string
    {
        DocumentFlowService::access();
        $this->setFirmParams();
        $id = $this->request->get('id') ?? 0;
        $this->params['type_options'] = DocumentTemplateType::getFilteredOptions([DocumentTemplateType::XML_TO_BKI]);
        $this->params['project_options'] = ProjectsModel::getOptions();
        $this->params['clause_options'] = ClausesTypes::getOptions();
        $this->params['dest_owner_options'] = FilialsModel::getOptions();
        $this->params['driver_trusted_options'] = DriverTrustedTypes::getOptions();
        $this->params['document_formats'] = DocumentFileExtensionsTypes::getSelectedOptions([
            DocumentFileExtensionsTypes::HTML,
        ]);

        $this->params['enableDriverTrusted'] = AccessService::check('personal', 'enableDriverTrusted');
        $this->setPageTitle([['title' => 'Шаблон', 'id' => $id, 'new_title' => 'Новый шаблон']]);
        return $this->renderReact(TemplatesService::get($id));
    }

    /**
     * @throws HttpException
     * @throws Exception
     * @api
     */
    public function actionChanges(): string
    {
        DocumentFlowService::access();
        $this->setFirmParams();
        $id = $this->request->get('id') ?? 0;
        $template = DocumentTemplatesModel::findOneSelect(['id' => $id], ['id']);
        if (!$template) {
            throw new NotFoundHttpException('Шаблон не найден');
        }
        $this->params['author_options'] = [
            ['key' => 0, 'value' => 'Неизвестно'],
            ...UsersCommonService::getUsersOptions(),
        ];
        $this->params['owner_id'] = $template->id;
        $this->setPageTitle([['title' => 'Шаблон', 'id' => $template->id], ['title' => 'Правки']]);
        return $this->renderReact();
    }
}
