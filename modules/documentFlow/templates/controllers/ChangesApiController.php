<?php

namespace modules\documentFlow\templates\controllers;

use modules\_common\abstractControllers\AbstractChangesApiController;
use modules\documentFlow\services\DocumentFlowService;
use modules\documentFlow\templates\services\DocumentTaskChangesService;

/**
 * @api
 */
class ChangesApiController extends AbstractChangesApiController
{
    /**
     * @inheritdoc
     * @return array
     * @throws \Exception
     */
    public function actionIndex(): array
    {
        DocumentFlowService::access();
        return parent::getChangesList(new DocumentTaskChangesService());
    }

    /**
     * @inheritdoc
     * @return array
     * @throws \Exception
     */
    public function actionNavigationTable(): array
    {
        DocumentFlowService::access();
        return parent::getNavigationChanges(new DocumentTaskChangesService());
    }

    /**
     * @inheritdoc
     * @return array
     * @throws \Exception
     */
    public function actionChanges(): array
    {
        DocumentFlowService::access();
        return parent::getChanges(new DocumentTaskChangesService());
    }

    /**
     * @inheritdoc
     * @return array
     * @throws \Exception
     */
    public function actionPagination(): array
    {
        DocumentFlowService::access();
        return parent::getPagination(new DocumentTaskChangesService());
    }
}
