<?php

namespace modules\documentFlow\templates\controllers;

use backend\validators\common\ValidatorHelpers;
use Exception;
use modules\_common\ItrController;
use modules\documentFlow\services\DocumentFlowService;
use modules\documentFlow\templates\services\TemplatesService;
use modules\documentFlow\templates\validators\GetTemplatesValidator;
use yii\web\HttpException;

/**
 * @api
 */
final class IndexController extends ItrController
{
    /**
     * Страница шаблонов
     * @throws HttpException
     * @throws Exception
     */
    public function actionIndex(): string
    {
        DocumentFlowService::access();
        $this->setFirmParams();
        $this->setRightMenuAccess();
        $this->setMainMenuAccess();
        $this->setPageTitle([['title' => 'Документооборот'], ['title' => 'Шаблоны']]);
        return $this->renderReact();
    }

    /**
     * Excel
     * @throws HttpException
     * @throws Exception
     */
    public function actionExcel(): string
    {
        DocumentFlowService::access();
        $validator = ValidatorHelpers::createFromGetJson(GetTemplatesValidator::class);
        if (!$validator->validate()) {
            throw new HttpException(400, 'Invalid request');
        }
        $response = TemplatesService::getList($validator);
        return $this->renderExcel('/TemplatesTable', ['tasks' => $response], 'document_flow_templates');
    }
}
