<?php

namespace modules\documentFlow\templates\controllers;

use common\models\DocumentTemplatesModel;
use common\services\common\StringService;
use modules\_common\ItrController;
use Yii;
use yii\web\NotFoundHttpException;

/**
 * @api
 */
class UploadedFileController extends ItrController
{
    /**
     * Получение файла шаблона
     * @throws NotFoundHttpException
     */
    public function actionIndex($fileHash): void
    {
        $this->setPageTitle([['title' => 'Шаблон']]);
        $pathPrefix = StringService::getPathPrefix($fileHash);
        $templatePath = Yii::getAlias('@root') . "/data/files/templates/{$pathPrefix}/{$fileHash}.html";
        if (!file_exists($templatePath)) {
            throw new NotFoundHttpException();
        }
        $fileName =
            DocumentTemplatesModel::findOneSelect(['template' => $fileHash], ['template_name'])?->template_name ?? '';
        Yii::$app->response->sendFile($templatePath, $fileName . '.' . 'html', ['inline' => false])->send();
    }
}
