<?php
namespace modules\documentFlow\templates\controllers;

use backend\controllers\ApiController;
use backend\validators\common\ValidatorHelpers;
use common\models\FinInvoicesModel;
use common\types\DocumentFileExtensionsTypes;
use Exception;
use modules\documentFlow\templates\services\TemplatesService;
use modules\documentFlow\templates\validators\FileUploadValidator;
use modules\documentFlow\templates\validators\GetTemplatesValidator;
use modules\documentFlow\templates\validators\SaveTemplateValidator;
use modules\settings\services\TemplateService;
use yii\web\UploadedFile;

class IndexApiController extends ApiController
{
    /**
     * Получение списка шаблонов
     * @return array[]
     * @throws Exception
     */
    public function actionIndex(): array
    {
        $validator = ValidatorHelpers::createFromPost(GetTemplatesValidator::class);
        if (!$validator->validate()) {
            return $this->getReactItrErrors($validator);
        }
        return TemplatesService::getList($validator);
    }

    /**
     * Пагинация
     * @throws Exception
     */
    public function actionPagination(): array
    {
        $validator = ValidatorHelpers::createFromPost(GetTemplatesValidator::class);
        if (!$validator->validate()) {
            return $this->getReactItrErrors($validator);
        }
        return TemplatesService::getPagination($validator);
    }

    /**
     * Сохранение шаблона
     * @throws Exception
     */
    public function actionSave(): array
    {
        $validator = ValidatorHelpers::createFromPost(SaveTemplateValidator::class);
        if (!$validator->validate()) {
            return $this->getReactItrErrors($validator);
        }
        $id = TemplatesService::save($validator);
        return TemplatesService::get($id);
    }

    /**
     * Справка о шаблонных выражениях
     * @return array
     * @api
     */
    public function actionGetInfo(): array
    {
        return TemplateService::getHints(new FinInvoicesModel());
    }

    /**
     * Загрузка файла
     * @throws Exception
     * @api
     */
    public function actionUpload(): array
    {
        $validator = ValidatorHelpers::createFromPost(FileUploadValidator::class);
        $validator->extensions = [DocumentFileExtensionsTypes::HTML];
        $validator->files = UploadedFile::getInstance($validator, 'file');
        if (!$validator->validate()) {
            return $this->getReactItrErrors($validator);
        }
        TemplatesService::uploadFile($validator);
        return TemplatesService::get($validator->id);
    }
}
