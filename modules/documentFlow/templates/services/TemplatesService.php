<?php
namespace modules\documentFlow\templates\services;

use common\models\ActiveQueryItr;
use common\models\DocumentTemplatesModel;
use common\models\DriversModel;
use common\models\FilesModel;
use common\models\FilialsModel;
use common\models\FinAccountsModel;
use common\models\FinInvoicesModel;
use common\models\ProjectsModel;
use common\models\UserProjectsModel;
use common\services\AutoParkService;
use common\services\common\DbOffsetService;
use common\services\common\PaginationService;
use common\services\common\StringService;
use common\services\CurrentUserService;
use common\services\FirmsService;
use common\types\ClausesTypes;
use common\types\DriverTrustedTypes;
use common\types\InvoiceDocumentTypes;
use common\types\OwnerTypes;
use Exception;
use modules\documentFlow\services\DocumentFlowService;
use modules\documentFlow\templates\validators\FileUploadValidator;
use modules\documentFlow\templates\validators\GetTemplatesValidator;
use modules\documentFlow\templates\validators\SaveTemplateValidator;
use Yii;
use yii\base\InvalidConfigException;
use yii\web\NotFoundHttpException;
use yii\web\UploadedFile;

class TemplatesService
{
    protected static int $limit = 100;

    /**
     * Получение списка шаблонов
     * @param GetTemplatesValidator $validator
     *
     * @return array
     * @throws Exception
     */
    public static function getList(GetTemplatesValidator $validator): array
    {
        $tasksQuery = self::getListQuery();
        $count = $tasksQuery->count();
        $offset = DbOffsetService::getOffset($validator->page * self::$limit, $count);
        $tasks = $tasksQuery
            ->limitOffset(self::$limit, $offset)
            ->orderBy('id desc')
            ->all();
        $result = [];
        /** @var DocumentTemplatesModel $task */
        foreach ($tasks as $task) {
            $result[] = [
                'id' => $task->id,
                'enabled' => $task->enabled,
                'deliverable' => $task->deliverable,
                'clauses' => $task->getClauseNames(),
                'acc_source_owner' => $task->getAccSourceOwnerType(),
                'acc_source_owner_group' => $task->getDriverTrusted(),
                'acc_dest_owner' => $task->getAccDestOwnerType(),
                'acc_dest_owners' => $task->getFilialsCount(),
                'template' => $task->template,
                'template_name' => $task->template_name,
                'template_link' => $task->getTemplateLink(),
                'projects' => $task->getProjectNames(),
            ];
        }
        return $result;
    }

    /**
     * Пагинация
     * @param GetTemplatesValidator $validator
     * @return array
     * @throws Exception
     */
    public static function getPagination(GetTemplatesValidator $validator): array
    {
        $tasksQuery = self::getListQuery();
        $count = $tasksQuery->count();
        $offset = DbOffsetService::getOffset($validator->page * self::$limit, $count);
        return [
            'count' => $count,
            'pagination' => PaginationService::create($count, self::$limit, $offset),
        ];
    }

    /**
     * Получение одного шаблона
     **
     * @param int $id
     * @return array
     * @throws NotFoundHttpException
     * @throws InvalidConfigException
     * @throws Exception
     */
    public static function get($id): array
    {
        $firm = AutoParkService::getInstance()->getCurrentPark();
        if (!$id) {
            $template = new DocumentTemplatesModel();
            $template->start_time = date('Y-m-d H:00:00');
            $template->next_time = date('Y-m-d H:i:00', strtotime(date('Y-m-d H:00:00') . ' +5 minutes'));
            $template->clauses = json_encode(ClausesTypes::getIds());
            $template->projects = json_encode(
                UserProjectsModel::find()
                    ->select(['project'])
                    ->where(['user' => CurrentUserService::getInstance()->get()->id])
                    ->asArray()
                    ->column(),
            );
            $template->acc_dest_owners = json_encode(
                FilialsModel::find()
                    ->select(['id'])
                    ->column(),
            );
            $template->acc_source_owner_group = json_encode(DriverTrustedTypes::getList());
        } else {
            $template = DocumentTemplatesModel::findOne(['id' => $id, 'firm' => $firm]);
        }
        if (!$template) {
            throw new NotFoundHttpException('Шаблон не найден');
        }
        $result = $template->attributes;
        $result['date_create'] = $id ? $template->getDateCreate() : '[Текущее время]';
        $result['clauses'] = $template->getClauses();
        $result['projects'] = $template->getProjects();
        $result['acc_dest_owners'] = $template->getAccDestOwners();
        $result['acc_source_owner_group'] = $template->getAccSourceOwnerGroup();
        $result['template_link'] = $template->getTemplateLink();

        return $result;
    }

    /**
     * Сохранение шаблона
     * @throws Exception
     */
    public static function save(SaveTemplateValidator $validator): int
    {
        $issetTask = self::issetValidation($validator, DocumentTemplatesModel::class);

        if ($validator->enabled && $issetTask) {
            throw new NotFoundHttpException('Такой шаблон уже есть');
        }

        if ($validator->id) {
            $task = DocumentTemplatesModel::findOne(['id' => $validator->id]);
        } else {
            $task = new DocumentTemplatesModel();
            $task->firm = AutoParkService::getInstance()->getCurrentPark();
        }
        $task->setAttributes($validator->attributes, false);
        $task->save();
        return $task->id;
    }

    /**
     * Создание документов "Счет на оплату" по шаблону
     * @throws Exception
     */
    public static function createFiles(int $type = InvoiceDocumentTypes::AUTO_INVOICE): void
    {
        $tasks = DocumentTemplatesModel::find()
            ->where(['enabled' => 1, 'type' => $type, 'auto' => 1])
            ->andWhere(['<', 'next_time', date('Y-m-d H:i:s')])
            ->andWhere(['!=', 'template', ''])
            ->all();
        DocumentFlowService::$documentAuthor = -4;
        foreach ($tasks as $task) {
            AutoParkService::getInstance()->setCurrentPark($task->firm);
            $invoices = self::getInvoices($task, $type);
            foreach ($invoices as $invoice) {
                DocumentFlowService::createDocument($invoice, $task, $type);
            }
            $task->next_time = date('Y-m-d H:i:s', strtotime('+1 hour'));
            $task->save();
        }
    }

    /**
     * Создание документов "Платежное поручение" по шаблону
     * @throws Exception
     */
    public static function createPaymentOrders(): void
    {
        self::createFiles(InvoiceDocumentTypes::AUTO_ORDER);
    }

    /**
     * @param  int  $type
     * @param  int  $invoiceId
     *
     * @return array
     */
    public static function getInvoicesIdsFromFiles(
        int $type = InvoiceDocumentTypes::AUTO_INVOICE,
        int $invoiceId = 0,
    ): array {
        $typeFilter = [$type];
        if ($type == InvoiceDocumentTypes::AUTO_ORDER) {
            $typeFilter = [InvoiceDocumentTypes::ORDER, InvoiceDocumentTypes::AUTO_ORDER];
        }
        $filesQuery = FilesModel::find()
            ->select('owner_id')
            ->where([
                'owner' => 'INVOICE',
                'type' => $typeFilter,
            ]);

        if ($invoiceId > 0) {
            $filesQuery->andWhere(['owner_id' => $invoiceId]);
        }

        $files = $filesQuery->asArray()->all();

        if (!count($files)) {
            return [];
        }

        $ids = [];
        foreach ($files as $file) {
            $ids[] = $file['owner_id'];
        }
        return $ids;
    }

    /**
     * Получение всчетов, для которых еще не созданы документы
     *
     * @param  DocumentTemplatesModel  $task
     * @param  int  $type
     *
     * @return array
     * @throws Exception
     */
    public static function getInvoices(
        DocumentTemplatesModel $task,
        int $type = InvoiceDocumentTypes::AUTO_INVOICE,
    ): array {
        $projects = json_decode($task->projects, true);
        $clauses = json_decode($task->clauses, true);
        $invoices = FinInvoicesModel::find()
            ->select(['i.*', 'a.number'])
            ->alias('i')
            ->leftJoin(FinAccountsModel::tableName() . ' a', 'a.id = i.acc_dest')
            ->where([
                'i.hidden' => 0,
                'i.acc_source_owner' => $task->acc_source_owner,
                'i.acc_dest_owner' => $task->acc_dest_owner,
                'i.clause' => $clauses,
                'a.owner' => $task->acc_dest_owner,
                'i.project' => $projects,
                'i.hide_in_documentflow' => false,
            ])
            ->andWhere(['NOT IN', 'i.id', self::getInvoicesIdsFromFiles($type)])
            ->andWhere(['!=', 'a.number', 'Наличный'])
            ->andWhere(['>', 'i.invoice_date', strtotime($task->start_time)]);
        if ($type == InvoiceDocumentTypes::AUTO_ORDER) {
            $invoices->andWhere(['i.one_c_status' => 4]);
        }
        if ($task->end_time !== null) {
            $invoices->andWhere(['<', 'i.invoice_date', strtotime($task->end_time)]);
        }
        if ($task->acc_source_owner == OwnerTypes::DRIVER) {
            $accSourceOwnerGroup = json_decode($task->acc_source_owner_group, true);
            $invoices
                ->leftJoin(DriversModel::tableName() . ' d', 'd.id = i.acc_source_owner_id')
                ->andWhere(['d.trusted' => $accSourceOwnerGroup]);
        }
        if ($task->acc_dest_owner == OwnerTypes::FILIAL) {
            $accDestOwners = json_decode($task->acc_dest_owners, true);
            $invoices
                ->leftJoin(FilialsModel::tableName() . ' f', 'f.id = i.acc_dest_owner_id')
                ->andWhere(['f.id' => $accDestOwners]);
        }
        return $invoices->all();
    }

    /**
     * Построение запроса для списка шаблонов
     *
     * @return ActiveQueryItr<DocumentTemplatesModel>
     * @throws Exception
     */
    protected static function getListQuery(): ActiveQueryItr
    {
        $firm = AutoParkService::getInstance()->getCurrentPark();
        return DocumentTemplatesModel::find()->where(['firm' => $firm]);
    }

    /**
     * Загрузка файлов
     * @throws Exception
     */
    public static function uploadFile(FileUploadValidator $validator): void
    {
        /** @var UploadedFile $fileValidator */
        $fileValidator = $validator->files;
        $task = DocumentTemplatesModel::findOne($validator->id);
        if ($task->template) {
            $pathPrefix = StringService::getPathPrefix($task->template);
            $templatePath = Yii::getAlias('@root') . "/data/files/templates/{$pathPrefix}";
            if (file_exists("{$templatePath}/{$task->template}.html")) {
                $fileSize = filesize("{$templatePath}/{$task->template}.html");
                unlink("{$templatePath}/{$task->template}.html");
                FirmsService::decrementDiskUsage($fileSize);
            }
        }
        $phys_name = StringService::getHashFileName($fileValidator->name);
        $pathPrefix = StringService::getPathPrefix($phys_name);
        $templatePath = Yii::getAlias('@root') . "/data/files/templates/{$pathPrefix}";
        if (file_exists("$templatePath/{$phys_name}.html")) {
            throw new Exception("Файл с названием {$fileValidator->name} уже существует");
        }
        if (!file_exists($templatePath)) {
            mkdir($templatePath, 0755, true);
        }
        $fileValidator->saveAs("$templatePath/{$phys_name}.html");

        $fileSize = filesize("$templatePath/{$phys_name}.html");
        FirmsService::incrementDiskUsage($fileSize);
        $task->template = $phys_name;
        $task->template_name = $validator->description;
        $task->date_upload_template = date('Y-m-d H:i:s');
        $task->save();
    }

    /**
     * Валидация дублирования карточек шаблона
     *
     * @param $validator
     * @param $table
     *
     * @return bool
     * @throws NotFoundHttpException
     * @throws Exception
     */
    public static function issetValidation($validator, $table): bool
    {
        if (!$validator->enabled) {
            return false;
        }
        $clauses = [];
        $templatesQuery = $table
            ::find()
            ->where([
                'firm' => AutoParkService::getInstance()->getCurrentPark(),
                'enabled' => 1,
                'acc_source_owner' => $validator->acc_source_owner,
                'acc_dest_owner' => $validator->acc_dest_owner,
            ])
            ->andWhere(['!=', 'id', $validator->id]);

        if (is_numeric($validator->clause)) {
            $templatesQuery->andWhere(['clause' => $validator->clause]);
        } else {
            $templatesQuery->andWhere(['type' => $validator->type]);
            $clauses = json_decode($validator->clauses, true);
        }
        $templates = $templatesQuery->all();

        $projects = json_decode($validator->projects, true);
        $trusted = json_decode($validator->acc_source_owner_group, true);
        $filials = json_decode($validator->acc_dest_owners, true);
        $tempCount = 1;
        foreach ($templates as $template) {
            $projectsIntersect = array_intersect($projects ?? [], json_decode($template->projects, true) ?? []);
            $trustedIntersect = array_intersect(
                $trusted ?? [],
                json_decode($template->acc_source_owner_group, true) ?? [],
            );
            $filialsIntersect = array_intersect($filials ?? [], json_decode($template->acc_dest_owners, true) ?? []);
            $clausesIntersect = [];
            if (count($clauses)) {
                $clausesIntersect = array_intersect($clauses, json_decode($template->clauses, true));
                if (!count($clausesIntersect) && $tempCount != count($templates)) {
                    $tempCount++;
                    continue;
                }
            }
            if (
                !count($projectsIntersect) ||
                ($template->acc_source_owner_group != null && !count($trustedIntersect)) ||
                ($template->acc_dest_owners != null && !count($filialsIntersect)) ||
                (count($clauses) && !count($clausesIntersect))
            ) {
                return false;
            }
            $errorMessage = '';
            if (count($clauses) && count($clausesIntersect)) {
                $clauseNames = self::getClauseNames($clausesIntersect);
                $errorMessage .= "$clauseNames, ";
            }
            if (count($projectsIntersect)) {
                $projectNames = self::getProjectNames($projectsIntersect);
                $errorMessage .= "$projectNames, ";
            }
            if (count($trustedIntersect)) {
                $errorMessage .= 'группы плательщиков, ';
            }
            if (count($filialsIntersect)) {
                $filialNames = self::getFilialNames($filialsIntersect);
                $errorMessage .= "$filialNames ";
            }
            throw new NotFoundHttpException("Шаблон для $errorMessage уже есть");
        }

        if (!$templates) {
            return false;
        }
        return true;
    }

    /**
     * Возвращает список наименований проектов
     * @param  array  $projects
     *
     * @return string
     */
    protected static function getProjectNames(array $projects): string
    {
        $result = [];
        foreach ($projects as $project) {
            $result[] = ProjectsModel::findOneSelect(['id' => $project], ['name'])?->name ?? '';
        }
        return implode(', ', $result);
    }

    /**
     * Возвращает наименование статьи
     * @param array $clauses
     * @return string
     */
    protected static function getClauseNames(array $clauses): string
    {
        $result = [];
        foreach ($clauses as $clause) {
            $result[] = ClausesTypes::get($clause);
        }
        return implode(', ', $result);
    }

    /**
     * Возвращает список наименований юрлиц
     * @param  array  $filials
     *
     * @return string
     */
    protected static function getFilialNames(array $filials): string
    {
        $result = [];
        foreach ($filials as $filial) {
            $result[] = FilialsModel::findOneSelect(['id' => $filial], ['name'])?->getName() ?? '';
        }
        return implode(', ', $result);
    }
}
