<?php
namespace modules\documentFlow\templates\validators;

use backend\validators\common\FilesValidator;
use common\types\FileSourceTypes;

class FileUploadValidator extends FilesValidator
{
    public string $source = FileSourceTypes::API;
    public string $owner = 'document_flow';
    
    /**
     * @inheritdoc
     * @return int
     */
    public function getMaxFiles(): int
    {
        return 1;
    }
}
