<?php
namespace modules\documentFlow\templates\validators;

use backend\filters\request\ArrayFilter;
use backend\validators\common\ArrayValidator;
use backend\validators\common\NumberValidator;
use common\types\OwnerTypes;
use yii\base\Model;

class SaveTemplateValidator extends Model
{
    public $id;
    public $enabled;
    public $deliverable;
    public $projects;
    public $clauses;
    public $clause;
    public $acc_source_owner;
    public $acc_source_owner_group;
    public $acc_dest_owner;
    public $acc_dest_owners;
    public $start_time;
    public $next_time;
    public $end_time;
    public $type;
    public $auto;

    public function rules(): array
    {
        return [
            [['id', 'end_time'], 'default', 'value' => null],
            [
                ['acc_source_owner', 'acc_dest_owner'],
                'default',
                'value' => '',
                'isEmpty' => function ($value) {
                    return !$value;
                },
            ],
            [['deliverable', 'enabled', 'auto'], 'default', 'value' => 0],
            [['projects', 'clauses', 'acc_dest_owners', 'acc_source_owner_group'], ArrayValidator::class],
            [['clause', 'type'], NumberValidator::class],
            [['acc_source_owner', 'acc_dest_owner'], 'string'],
            [
                ['projects', 'clauses'],
                'required',
                'isEmpty' => function ($value) {
                    return !$value;
                },
            ],
            [
                ['acc_source_owner_group'],
                'required',
                'isEmpty' => function ($value) {
                    return !$value;
                },
                'when' => function () {
                    return $this->acc_source_owner == OwnerTypes::DRIVER;
                },
            ],
            [
                ['acc_dest_owners'],
                'required',
                'isEmpty' => function ($value) {
                    return !$value;
                },
                'when' => function () {
                    return $this->acc_dest_owner == OwnerTypes::FILIAL;
                },
            ],
            [
                ['acc_source_owner', 'acc_dest_owner', 'start_time', 'next_time', 'type'],
                'required',
                'isEmpty' => function ($value) {
                    return !$value;
                },
            ],
            [
                ['acc_source_owner_group'],
                'required',
                'when' => function ($model) {
                    return $model->acc_source_owner == OwnerTypes::DRIVER;
                },
            ],
            [
                ['acc_dest_owners'],
                'required',
                'when' => function ($model) {
                    return $model->acc_dest_owner == OwnerTypes::FILIAL;
                },
            ],
            [
                ['projects', 'clauses', 'acc_dest_owners', 'acc_source_owner_group'],
                ArrayFilter::class,
                'skipOnEmpty' => false,
            ],
        ];
    }

    public function attributeLabels(): array
    {
        return [
            'clauses' => 'Статьи',
            'projects' => 'Проекты',
            'acc_source_owner' => 'Тип плательщика',
            'acc_dest_owner' => 'Тип получателя',
            'acc_source_owner_group' => 'Свои/Не свои',
            'acc_dest_owners' => 'Тип получателя - юрлица',
            'start_time' => 'Дата начала',
            'next_time' => 'Следующее выполнение',
            'type' => 'Тип',
        ];
    }
}
