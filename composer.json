{"name": "yiisoft/yii2-app-advanced", "description": "Yii 2 Advanced Project Template", "keywords": ["yii2", "framework", "advanced", "project template"], "homepage": "http://www.yiiframework.com/", "type": "project", "license": "BSD-3-<PERSON><PERSON>", "support": {"issues": "https://github.com/yiisoft/yii2/issues?state=open", "forum": "http://www.yiiframework.com/forum/", "wiki": "http://www.yiiframework.com/wiki/", "irc": "irc://irc.freenode.net/yii", "source": "https://github.com/yiisoft/yii2"}, "minimum-stability": "stable", "require": {"php": ">=8.0", "yiisoft/yii2": "~2.0.43", "yiisoft/yii2-bootstrap4": "~2.0.0", "yiisoft/yii2-swiftmailer": "~2.0.0 || ~2.1.0", "yiisoft/yii2-sphinx": "^2.0", "telegram-bot/api": "^2.3", "ext-curl": "*", "box/spout": "^3.3", "symfony/mailer": "^6.0", "dompdf/dompdf": "^1.2", "symfony/dotenv": "^6.0", "tecnickcom/tc-lib-barcode": "^1.17", "zircote/swagger-php": "^4.11", "doctrine/annotations": "^1.14", "aws/aws-sdk-php": "^3.218"}, "require-dev": {"yiisoft/yii2-debug": "~2.1.0", "yiisoft/yii2-gii": "~2.2.0", "yiisoft/yii2-faker": "~2.0.0", "codeception/codeception": "^4.2.2", "codeception/module-asserts": "^1.0", "codeception/module-yii2": "^1.1.5", "codeception/module-filesystem": "^1.0", "codeception/verify": "~0.5.0 || ~1.1.0", "symfony/browser-kit": ">=2.7 <=4.2.4", "codeception/module-phpbrowser": "^1.0", "codeception/module-rest": "^1.3", "mockery/mockery": "^1.6", "phpstan/phpstan": "^2.1"}, "config": {"process-timeout": 1800, "fxp-asset": {"enabled": false}, "allow-plugins": {"yiisoft/yii2-composer": true}}, "repositories": [{"type": "composer", "url": "https://asset-packagist.org"}], "autoload": {"psr-4": {"modules\\": "modules/", "backend\\": "backend/", "common\\": "common/"}}}