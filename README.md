## Инициализация проекта

### Внимание разработчикам на ARM процессорах.

### Используйте оф.докер клиент для эмуляции образов на основе Rosetta.
- <NAME_EMAIL>:PhantomSoft/itr.git
- Прописать пути в переменных докера до проекта
- Запустить docker
- Зайти в docker командой `docker exec -it itransport_api bash`
- Перейти в папку проекта `cd /www/api.itransport.top/`
- Выполнить команду `composer install`
- Сделать conf.ts на основе conf.ts.example

## Создание миграции

- Зайти в docker командой `docker exec -it itransport_api bash`
- Перейти в папку проекта `cd /www/api.itransport.top/`
- Выполнить команду `php yii migrate/create <название>`
- Открыть созданный файл и реализовать логику миграции

Более подробно читайте: https://www.yiiframework.com/doc/guide/2.0/ru/db-migrations

## Sphinx

Запуск индексатора
docker exec -it itransport_sphinx indexer --config /opt/sphinx/conf/sphinx.conf --rotate --all

генерация конфига
php yii sphinx/generate-config


# S3
Для локалки используйте minio из докер проекта
Не забудьте добавить secretKey,accessKey в `common/config/s3.php`

А так же ENV_S3_BUCKET_KEY в `common/config/env.local.php`

Для дебага и подключения к бакетам из PHPSTORM можно использовать плагин.

Установить нужно **[BIG DATA TOOLS CORE](https://plugins.jetbrains.com/plugin/21713-big-data-tools-core) | [REMOTE FILES SYSTEMS](https://plugins.jetbrains.com/plugin/21706-remote-file-systems) | [FILE VIEWER](https://plugins.jetbrains.com/plugin/21701-big-data-file-viewer)**

## Тестирование на srv3

### Восстановление базы из ночного бекапа

1. заходим на хост машину по ssh - `ssh <EMAIL>`, порт стандартный (22)
2. смотрим какие есть бэкапы, выбираем нужный: `ls -ls /var/lib/bckp/mysql/srv1.itransport.top` - в имени файла замечаем дату его, например 20211025
3. Определяемся какой инстанс заливать: mysql - test1.itransport.top (порт 3306), mysql2 - test2.itransport.top (порт 3307)
4. запускаем скрипт `sudo -u root /usr/local/bin/make_new_database mysql2 20211025`
5. как скрипт закончит работать, проверяем что база взлетела.
6. если что пошло не так, присылаем Роме весь вывод, включая саму команду.
7. не заливайте одновременно 2 базы 🙂

## Тесты

### REST тесты

~~Чтобы работали тесты нужно в докере прописать host основной машины~~

~~получить IP ifconfig eth0 | grep inet | sed -e "s/.*inet \([^ ]*\) .*/\1/")~~

~~echo "<полученный IP> local.api.itransport.top" >> /etc/hosts~~

### Запуск тестов

#### Запуск REST API тестов из папки `backend` внутри докера
```bash
cd /www/api.itransport.top/backend/
../vendor/codeception/codeception/codecept run unit
```

#### Запуск unit тестов снаружи

```bash
docker exec -it itransport_api sh -c "cd /www/api.itransport.top/backend/ && ../vendor/codeception/codeception/codecept run unit"
````

## Статический анализ

#### Запуск статического анализа
```vendor/bin/phpstan analyse```

## Mix

на windows установить `npm install -g win-node-env`

Запустить `npm run hot`

Открыть
local.api.itransport.top:3000

Структура
- backend
    - config
        - main.php (конфиг для роутов, по умолчанию <module>/<controller>/<action>/ и <module>/)
- common
    - config
        - main.php (конфиг для подключения модулей)
- modules
    - _common
    - <module_name>
        - controllers (yii контроллеры)
          - ApiController.php (rest контроллер)
          - IndexController.php (plain контроллер)
        - view (папка с react)
        - <Name>Module.php
- app.tsx (точка входа react)

## Загрузка шрифтов
```
cd libFiles/dompdf/
php load_font.php Arial ./Arial.ttf
```

## Генерация триггеров
Все пользовательские триггеры хранятся в каталоге [triggers](libFiles%2Ftriggers) в соответствующих подкаталогах

По умолчанию в таблицах отслеживаются все поля кроме `id` и `data` (так что не называйте так поля в таблицах, если в ней
предусматривается отслеживание правок в этих полях), а также поля с типом `longtext`.

Для указания дополнительных полей, игнорируемых в правках, добавьте в модель таблицы *сразу после метода* `tableName()`
метод

```
/**
 * Возвращает поля, игнорируемые в правках
 * @return string[]
 */
public static function exceptTriggerFields(): array
{
    return ['next_time', 'secure_field'];
}
```

#### Хранимые Процедуры БД

Если у вас отсутствуют процедуры, вы можете импортировать их из [storage](libFiles%2Ftriggers%2Fstorage).

Если миграция создает новые таблицы (или изменяет структуру) и в таблицах надо отслеживать правки или применить другие
триггеры, то в миграцию после манипуляций с БД включите код генерации триггеров

#### Применить все доступные триггеры для таблицы `cars`

`TriggersService::applyTriggers('cars');`

#### Применить все доступные триггеры для таблиц `cars` и `drivers`

`TriggersService::applyTriggers(['cars', 'drivers']);`

#### Удалить все триггеры для таблицы `cars`

`TriggersService::removeTriggers('cars', true);`

#### Удалить триггеры отслеживания правок для таблицы `cars`, но применить пользовательские

`TriggersService::removeTriggers('cars');`

#### Применить триггеры отслеживания правок для таблицы `cars`, но удалить пользовательские

`TriggersService::applyTriggers('cars', true, false);`

#### Для применения триггеров для общих(не парковых) таблиц укажите имя БД

`TriggersService::applyTriggers('taxist.users');`

#### А вот так лучше не делать. Оно-то сработает, но это ж таблица логов, зачем логи отслеживать, если они не меняются!

`TriggersService::applyTriggers('logs.users_log');`
