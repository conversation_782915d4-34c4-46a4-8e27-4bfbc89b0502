#!/usr/bin/env bash
set -x

BRANCH=$1
IS_STAGE=$2

git fetch --all \
&& git checkout $BRANCH \
&& git pull \
&& npm i --no-save \
&& npm run prod \
&& rsync -a --delete backend/web/tmp/app.js backend/web/js/app.js \
&& /usr/local/php-8.0/bin/php composer.phar install \
&& /usr/local/php-8.0/bin/php yii migrate --interactive=false \
&& /usr/local/php-8.0/bin/php yii db-audit \
&& cd ../itransport.top \
&& git fetch --all \
&& git checkout $BRANCH \
&& git pull \
&& cd ../conf \
&& git fetch --all \
&& git checkout $BRANCH \
&& git pull \
&& cd ../api.itransport.top \
$SHELL

if [ "$IS_STAGE" == "--stage" ]; then
  cd /www/api.itransport.top \
  && /usr/local/php-8.0/bin/php vendor/bin/phpstan analyse \
  && cd backend \
  && /usr/local/php-8.0/bin/php ../vendor/codeception/codeception/codecept run unit \
  && cd ../
fi
