{"prettier": {"printWidth": 120, "trailingComma": "all", "phpVersion": "8.0", "singleQuote": true}, "scripts": {"dev": "NODE_ENV=development webpack --progress --config=node_modules/laravel-mix/setup/webpack.config.js", "watch": "NODE_ENV=development webpack --watch --progress  --config=node_modules/laravel-mix/setup/webpack.config.js", "hot": "NODE_ENV=development webpack-dev-server --hot --config=node_modules/laravel-mix/setup/webpack.config.js", "hot2": "NODE_ENV=development webpack-dev-server --hot --config=webpack.config.js", "prod": "NODE_ENV=production webpack --progress --config=node_modules/laravel-mix/setup/webpack.config.js"}, "devDependencies": {"@babel/preset-react": "^7.17.12", "@pmmmwh/react-refresh-webpack-plugin": "^0.5.7", "@prettier/plugin-php": "^0.18.8", "@types/jquery": "^3.5.14", "@types/react": "^18.2.22", "browser-sync": "^2.27.10", "browser-sync-webpack-plugin": "^2.3.0", "classnames": "^2.3.2", "dotenv-webpack": "^8.0.1", "file-loader": "^6.2.0", "filemanager-webpack-plugin": "^7.0.0", "laravel-mix": "^6.0.48", "node-sass": "^7.0.1", "prettier": "^2.7.0", "replace-in-file": "^8.3.0", "resolve-url-loader": "^5.0.0", "sass-loader": "^12.6.0", "ts-loader": "^9.3.0"}, "dependencies": {"axios": "^0.27.2", "chosen-js": "^1.8.7", "clsx": "^2.1.1", "dotenv": "^16.0.2", "fslightbox-react": "^1.7.4", "i18next": "^23.2.11", "inversify": "^6.0.1", "inversify-inject-decorators": "^3.1.0", "jquery": "^3.6.0", "js-cookie": "^3.0.1", "mobx": "^6.6.0", "mobx-react-lite": "^3.4.0", "postcss-preset-env": "^10.0.8", "react": "^18.1.0", "react-color": "^2.19.3", "react-datepicker": "^4.12.0", "react-dom": "^18.1.0", "react-i18next": "^13.0.2", "react-refresh": "^0.13.0", "react-router-dom": "^6.3.0", "reactcss": "^1.2.3", "reflect-metadata": "^0.1.13", "sass": "^1.62.1", "tailwindcss": "^3.4.14", "tooltipster": "^4.2.8", "typescript": "^4.7.3", "universal-cookie": "^4.0.4"}, "types": "./index.d.ts", "name": "itr", "description": "### Внимание разработчикам на ARM процессорах.", "version": "1.0.0", "main": "tailwind.config.js", "directories": {"doc": "docs"}, "repository": {"type": "git", "url": "git+https://github.com/PhantomSoft/itr.git"}, "author": "", "license": "ISC", "bugs": {"url": "https://github.com/PhantomSoft/itr/issues"}, "homepage": "https://github.com/PhantomSoft/itr#readme"}