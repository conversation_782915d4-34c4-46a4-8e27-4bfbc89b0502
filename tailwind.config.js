/** @type {import('tailwindcss').Config} */
module.exports = {
  content: ['./modules/**/*.{ts,tsx}'],
  variants: {
    extend: {
      backgroundOpacity: ['active'],
    },
  },
  theme: {
    extend: {
      colors: {
        gray: '#aaa',
      },
      spacing: {
        selector: '21px',
        inputChosen: '34px',
        inputChosenMarginTop: '7px',
        inputSmallChosenMarginTop: '28px',
        inputSmallChosenMarginLeft: '47.6%',
        inputAnchorTop: '-4.2rem',
      },
      zIndex: { 100000: 100000 },
    },
  },
  plugins: [
    function ({ addUtilities }) {
      addUtilities({
        '.comboboxButtonActiveGradient': {
          backgroundImage: 'linear-gradient( #eeeeee 20%, #ffffff 80%)',
          boxShadow: '0 1px 0 #FFF inset',
          borderTopLeftRadius: '0.125rem',
          borderTopRightRadius: '0.125rem',
          border: '1px solid #aaa',
          borderBottom: '0px',
          borderColor: '#AAA !important',
        },
        '.comboboxButtonActiveGradientReverse': {
          backgroundImage: 'linear-gradient( #ffffff 20%, #eeeeee 80%)',
          boxShadow: '0 1px 0 #FFF inset',
          borderBottomLeftRadius: '0.125rem',
          borderBottomRightRadius: '0.125rem',
          borderTop: '0px',
          borderColor: '#AAA !important',
        },
        '.chosenInput': {
          borderStyle: 'solid',
          borderColor: '#aaa !important',
          borderWidth: '1px',
          borderTop: 'none !important',
          borderBottom: 'none !important',
          borderRadius: '0px',
        },
        '.modal': {
          position: 'fixed !important',
          top: '50%',
          left: '50%',
          transform: 'translate(-50%, -50%)',
          zIndex: '1050',
          width: '560px',
          backgroundColor: '#fffde7',
          border: '3px solid #5890c1',
          borderRadius: '6px',
          outline: 'none',
          boxShadow: '0 3px 7px rgba(0, 0, 0, 0.3)',
          backgroundClip: 'padding-box',
        },
      });
    },
  ],
};
