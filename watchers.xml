<!--Импортировать в settings -> tools -> file watcher-->
<TaskOptions>
  <TaskOptions>
    <option name="arguments" value="$ProjectFileDir$/utils/replaceYo.mjs $FilePath$" />
    <option name="checkSyntaxErrors" value="true" />
    <option name="description" />
    <option name="exitCodeBehavior" value="ERROR" />
    <option name="fileExtension" value="*" />
    <option name="immediateSync" value="true" />
    <option name="name" value="replace yo" />
    <option name="output" value="" />
    <option name="outputFilters">
      <array />
    </option>
    <option name="outputFromStdout" value="false" />
    <!--После импорта заменить путь до node.js-->
    <option name="program" value="$PROJECT_DIR$/../../../Program Files/nodejs/node" />
    <option name="runOnExternalChanges" value="true" />
    <option name="scopeName" value="Current File" />
    <option name="trackOnlyRoot" value="false" />
    <option name="workingDir" value="$ProjectFileDir$" />
    <envs />
  </TaskOptions>
</TaskOptions>
