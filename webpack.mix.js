const path = require('path');
const mix = require('laravel-mix');
const FileManagerPlugin = require('filemanager-webpack-plugin');
require('dotenv').config({ path: './.env' });

const proxyUrl = process.env.PROXY_URL || 'local.api.itransport.top:8000';

if (mix.inProduction()) {
  mix.ts('modules/app.tsx', 'backend/web/tmp').react();
} else {
  mix.ts('modules/app.tsx', 'backend/web/js').react();
}
mix.css('modules/_common/view/index.css', 'backend/web/assets/css').react();

if (!mix.inProduction()) {
  //TODO: Отобразить детали ошибок в overlay из консоли.
  mix.sourceMaps().browserSync(proxyUrl);
} else {
  mix.setPublicPath(path.resolve('./'));
  /**
   * hack для перемещения картинок из css
   */
  mix.webpackConfig({
    plugins: [
      new FileManagerPlugin({
        events: {
          onEnd: {
            move: [
              {
                source: path.resolve('./images'),
                destination: path.resolve('./backend/web/images'),
              },
            ],
          },
        },
      }),
    ],
  });
  mix.version();
}
