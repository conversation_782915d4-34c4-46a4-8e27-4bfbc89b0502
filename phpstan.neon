parameters:
    level: 9
    bootstrapFiles:
        - vendor/autoload.php
    scanFiles:
        - _ide_helper.php
    scanDirectories:
        - backend
        - common
        - console
    paths:
        - modules/car/tests
        - modules/car/issuances
        - modules/cars/directory/customGroups
        - modules/contragents/banks
        - modules/debit
        - modules/driver/issuances
        - modules/exchange/purses
        - modules/filials/card/accounting
        - modules/fines
        - modules/issuances
        - modules/management/accounts/
        - modules/management/clauses
        - modules/management/orders
        - modules/permissionRequests
        - modules/reports/park/issuances
        - vendor/autoload.php
    excludePaths:
        - vendor/*
        - modules/fines/index/validators
        - modules/management/clauses/validators
        - *.view.php


services:
    -
        class: backend\rules\RequireStrictTypesDeclarationRule
        arguments: #Добавить конфиг для strict types
            -
                - modules/car/tests
                - modules/car/issuances
                - modules/contragents/banks
                - modules/driver/issuances
                - modules/issuances
                - modules/reports/park/issuances
        tags:
            - phpstan.rules.rule
    -
        class: backend\rules\UnusedImportsRule
        tags:
            - phpstan.rules.rule
