# phpstorm project files
.idea
*.iml
# netbeans project files
nbproject
# zend studio for eclipse project files
.buildpath
.project
.settings
# sublime text project / workspace files
*.sublime-project
*.sublime-workspace
# visual studio code project files
.vscode
# windows thumbnail cache
Thumbs.db
# composer vendor dir
/vendor
# cubrid install dir
/cubrid
# composer itself is not needed
composer.phar

# dependencies
/frontend/node_modules
/frontend/.pnp
/frontend/.pnp.js

# testing
/frontend/coverage

# production
/frontend/build

# cache
/backend/runtime/cache

# misc
.DS_Store
.env.local
.env.development.local
.env.test.local
.env.production.local

npm-debug.log*
yarn-debug.log*
yarn-error.log*

_generated
/backend/runtime/cache/
/console/runtime/cache/
/data/
hot
mix-manifest.json
/conf.ts
/backend/web/images

node_modules
/.env

.env
/common/config/.env.local.php
/backend/web/assets/*
/backend/web/css/
!/backend/web/assets/css
!/backend/web/assets/fonts
!/backend/web/assets/ico
!/backend/web/assets/images
!/backend/web/assets/img
!/backend/web/assets/js
!/backend/web/assets/docs
/images/
/.nvmrc
/backend/web/js/
/backend/web/js/app.js
/backend/web/js/app.js.LICENSE.txt
/backend/web/tmp/
/backend/web/assets/css/index.css
/common/config/s3.php
